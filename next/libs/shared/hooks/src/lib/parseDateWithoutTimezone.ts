export const parseDateWithoutTimezone = (dateInput: string | number): Date => {
  if (!dateInput && dateInput !== 0) {
    throw new Error('Invalid date input');
  }

  let year: number, month: number, day: number;

  if (typeof dateInput === 'number') {
    // Handle numbers as timestamps
    const rawDate = new Date(dateInput);
    if (isNaN(rawDate.getTime())) {
      throw new Error('Invalid timestamp');
    }
    year = rawDate.getFullYear();
    month = rawDate.getMonth();
    day = rawDate.getDate();
  } else if (typeof dateInput === 'string') {
    const normalizedInput = dateInput.trim();

    // Define regex patterns for different date formats
    const isoRegex = /^\d{4}-\d{2}-\d{2}/; // ISO (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ssZ)
    const usRegex = /^\d{2}\/\d{2}\/\d{4}$/; // US format (MM/DD/YYYY)
    const longDateRegex = /^[A-Za-z]+\s\d{1,2},\s\d{4}$/; // Long format (e.g., "December 9, 2024")

    if (isoRegex.test(normalizedInput)) {
      // Extract only the date part if time is present
      const datePart = normalizedInput.split('T')[0];
      const [y, m, d] = datePart.split('-').map(Number);
      year = y;
      month = m - 1;
      day = d;
    } else if (usRegex.test(normalizedInput)) {
      // Parse US format
      const [m, d, y] = normalizedInput.split('/').map(Number);
      year = y;
      month = m - 1;
      day = d;
    } else if (longDateRegex.test(normalizedInput)) {
      // Parse long date formats
      const parsedDate = new Date(normalizedInput);
      if (isNaN(parsedDate.getTime())) {
        throw new Error('Invalid long date format');
      }
      year = parsedDate.getFullYear();
      month = parsedDate.getMonth();
      day = parsedDate.getDate();
    } else {
      // Additional formats
      const euroRegex = /^\d{2}\/\d{2}\/\d{4}$/; // DD/MM/YYYY
      const euroDashRegex = /^\d{2}-\d{2}-\d{4}$/; // DD-MM-YYYY
      const ymdSlashRegex = /^\d{4}\/\d{2}\/\d{2}$/; // YYYY/MM/DD
      const usDashRegex = /^\d{2}-\d{2}-\d{4}$/; // MM-DD-YYYY

      if (usDashRegex.test(normalizedInput)) {
        // Parse MM-DD-YYYY
        const [m, d, y] = normalizedInput.split('-').map(Number);
        year = y;
        month = m - 1;
        day = d;
      } else if (euroRegex.test(normalizedInput)) {
        // Parse DD/MM/YYYY
        const [d, m, y] = normalizedInput.split('/').map(Number);
        year = y;
        month = m - 1;
        day = d;
      } else if (euroDashRegex.test(normalizedInput)) {
        // Parse DD-MM-YYYY
        const [d, m, y] = normalizedInput.split('-').map(Number);
        year = y;
        month = m - 1;
        day = d;
      } else if (ymdSlashRegex.test(normalizedInput)) {
        // Parse YYYY/MM/DD
        const [y, m, d] = normalizedInput.split('/').map(Number);
        year = y;
        month = m - 1;
        day = d;
      } else {
        // Fallback for unrecognized formats
        const parsedDate = new Date(normalizedInput);
        if (isNaN(parsedDate.getTime())) {
          throw new Error('Unsupported date format');
        }
        year = parsedDate.getFullYear();
        month = parsedDate.getMonth();
        day = parsedDate.getDate();
      }
    }
  } else {
    throw new Error('Invalid input type');
  }

  // Return a Date object with no time component
  return new Date(year, month, day);
};
