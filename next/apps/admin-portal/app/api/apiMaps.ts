import { ApiService, generateApiConfig } from './apiMapsHelper';

/**
 * Parameter Types:
 * - `pathParams`: Parameters added directly to the endpoint path (e.g., `/resource/:id` → `{ id: 123 }`).
 * - `queryParams`: Optional parameters appended as a query string (e.g., `?limit=10&offset=0`).
 */

/**
 * Raw API configuration:
 * - Each entry specifies the service and resource path.
 * - Use {@link NoPathParams} and {@link HasIdParam} for specific parameter definitions.
 */
const rawApiConfig: Record<string, [ApiService, string]> = {
  /**
   * @endpoint Organization
   * @description Fetches organization details.
   * @pathParams {@link HasIdParam}
   * @queryParams {@link OrganizationQueryParams}
   */
  organization: [ApiService.BenAdmin, 'organization'],

  /**
   * @endpoint Document
   * @description Fetches documents associated with a plan
   * @pathParams {@link NoPathParams}
   * @queryParams {@link PlanDocumentQueryParams}
   */
  document: [ApiService.BenAdmin, 'document'],

  pdx: [ApiService.BenAdmin, 'document/pdx'],

  pbc: [ApiService.BenAdmin, 'document/pbc'],

  pbc_plan: [ApiService.BenAdmin, 'document/pbc_plan'],

  esixml: [ApiService.BenAdmin, 'document/esixml'],

  bpl_transmission: [ApiService.BenAdmin, 'bpl_transmission'],

  /**
   * @endpoint File Download
   * @description Downloads plan document
   * @pathParams {@link HasIdParam}
   */
  fileDownload: [ApiService.BenAdmin, 'file-download'],

  /**
   * @endpoint Change Request
   * @description Retrieves Change Request
   * @pathParams {@link HasIdParam}
   */
  changerequest: [ApiService.BenAdmin, 'changerequest'],

  /**
   * @endpoint Plan
   * @description Retrieves Plan for Ben Admin
   * @pathParams {@link HasIdParam}
   */
  plan: [ApiService.BenAdmin, 'plan'],

  contacts: [ApiService.BenAdmin, 'contacts'],

  /**
   * @endpoint Publish
   * @description Publishes a Change Request for Ben Admin
   * @pathParams {@link HasIdParam}
   */
  publish: [ApiService.BenAdmin, 'changerequest/:id/publish'],

  /**
   * @endpoint Document
   * @description Fetches documents associated with a plan
   * @pathParams {@link NoPathParams}
   * @queryParams {@link PicklistQueryParams}
   */
  picklist: [ApiService.BenAdmin, 'picklist'],

  /**
   * @endpoint Plan Design
   * @description Creates plan design
   * @pathParams id: The ID of the plan
   */
  planDesign: [ApiService.BenAdmin, 'plan/:id/plan_design'],

  /**
   * @endpoint Add on Products
   * @description Gets Add on Products using plan_id
   * @pathParams none
   */
  addOnProduct: [ApiService.BenAdmin, 'add_on_product'],

  /**
   * @endpoint Plan Designs
   * @description Retrieves all Plan Designs using change request id
   * @pathParams id: The ID of the change request
   */
  changeContent: [ApiService.BenAdmin, 'changerequest/:id/change_content'],

  /**
   * @endpoint Plan Ancillaries
   * @description Creates Plan Ancillaries using plan_id and selected Add-On Product body
   * @pathParams id: The ID of the plan
   */
  planDesignAncillary: [ApiService.BenAdmin, 'plan/:id/plan_design_ancillary'],

  /**
   * @endpoint Change Request Validation
   * @description Validates the entire change request and returns results for each page
   * @pathParams id: This is the change request id
   */
  validate: [ApiService.BenAdmin, 'changerequest/:id/validate'],

  /**
   * @endpoint Change Request Help Text
   * @description Retrieves help text for the change request form
   * @pathParams id: Change Request ID
   */
  helpText: [ApiService.BenAdmin, 'changerequest/:id/screen-accessed'],

  /**
   * @endpoint Identifier for new objects
   * @description Retrieves a single string identifier for new objects. Treat this string as a primary key
   * @queryParams type: The type of object you wish to create. (cost_share_tier...etc.)
   */
  identifier: [ApiService.BenAdmin, 'identifier'],

  existingPlanDesignsFromChangeRequest: [
    ApiService.BenAdmin,
    'changerequest/:id/plan-designs',
  ],
};

/**
 * Centralized API configuration:
 * - Automatically generated by processing `rawApiConfig`.
 */
export const apiConfig = generateApiConfig(rawApiConfig);

// Export rawApiConfig for internal use (e.g., fallback path generation)
export { rawApiConfig };
