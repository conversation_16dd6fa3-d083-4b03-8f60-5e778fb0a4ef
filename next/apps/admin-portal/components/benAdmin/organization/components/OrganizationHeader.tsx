// components/OrganizationHeader.tsx
import { Heading } from '@chakra-ui/react';

import { useIsChangeRequestPage } from '../hooks/useOrganizationHooks';

type OrganizationHeaderProps = {
  orgName: string | null;
};

export const OrganizationHeader = ({ orgName }: OrganizationHeaderProps) => {
  const isChangeRequestPage = useIsChangeRequestPage();

  return (
    <>
      {!isChangeRequestPage && (
        <Heading size="lg" m={4}>
          {orgName}
        </Heading>
      )}
    </>
  );
};
