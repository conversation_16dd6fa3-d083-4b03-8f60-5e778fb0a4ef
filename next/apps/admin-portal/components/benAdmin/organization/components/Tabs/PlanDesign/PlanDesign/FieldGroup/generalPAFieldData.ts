import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';
import { z } from 'zod';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { currencyValidation } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/validations';
import { getGeneralPAPaths } from '../Config/generalPAConfig';

export function getGeneralPAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getGeneralPAPaths(selectedIndex);

  const productName = planData?.plan?.product?.name;

  // Use map with early return pattern, then filter out null values
  return planDesignDetails
    .map((designDetail) => {
      return {
        subtitle: `Pharmacy Accumulator - General - ${planDesign?.name || ''}`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          ...(productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360 ||
          productName === productNames.IRX_360
            ? [
                {
                  label: 'Accumulators Integrated with Medical',
                  value: designDetail?.accum_integrated_ind,
                  name: planDesignConfig.accum_integrated_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                  infoText: 'Accumulators Integrated with Medical',
                },
              ]
            : []),
          ...(productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360 ||
          productName === productNames.IRX_360
            ? [
                {
                  label: 'Plan Max Coverage Amount',
                  value: designDetail?.max_coverage_amount,
                  name: planDesignConfig.max_coverage_amount,
                  type: 'input' as const,
                  placeholder: 'Enter Plan Max Coverage Amount',
                  infoText: 'Plan Max Coverage Amount',
                  validations: currencyValidation.optional(),
                },
                {
                  label: 'Medical Integration Tier',
                  value: designDetail?.medical_integration_tier_ind,
                  name: planDesignConfig.medical_integration_tier_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.medicalIntegrationTierMap,
                  infoText: 'Medical Integration Tier',
                  validations: z.string().optional(),
                },
              ]
            : []),
          ...(productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360
            ? [
                {
                  label: 'Separate Accums Retail and IHP',
                  value: designDetail?.separate_accums_retail_ihp_ind,
                  name: planDesignConfig.separate_accums_retail_ihp_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                  placeholder: 'Select Separate Accums Retail and IHP',
                  infoText: 'Do you want to separate Accums Retail and IHP ?',
                },
                {
                  label: 'IHP Ded Cross-Apply to Non-IHP Ded',
                  value: designDetail?.ihp_cross_apply_ind,
                  name: planDesignConfig.ihp_cross_apply_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                  placeholder: 'Select IHP Ded Cross-Apply to Non-IHP Ded',
                  infoText:
                    'Do you want IHP Deduction to Cross-Apply for Non-IHP Deduction',
                },
              ]
            : []),
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[]; // Remove null values and assert type
}
