// generateTierFields.ts
import {
  Box,
  Button,
  Modal,
  ModalBody,
  ModalClose<PERSON>utton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Text,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import {
  InlineEditColumn,
  InlineEditTable,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/FormTable';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { bopTablestyles } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Styles/styles';
import React, { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { getCostShareColumns } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/PlanDesign/PatientPay/CostShare/useCostShareColumns';
import {
  filterTiersByPrePackaged,
  transformTierForDisplay,
} from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/PlanDesign/PatientPay/CostShare/utils/costShareUtils';

// Column width constraint
const COLUMN_WIDTH = '200px';

// No-op function for read-only tables
const noop = () => null;

// Read-only Cost Share Table Modal Component
const CostShareTableModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  tierType: string;
  tierCount: number;
  tableData: any[];
  columns: InlineEditColumn[];
}> = ({ isOpen, onClose, tierType, tierCount, tableData, columns }) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="6xl" scrollBehavior="inside">
      <ModalOverlay />
      <ModalContent maxW="80vw" maxH="80vh">
        <ModalHeader>Patient Pay - {tierType} Cost Share Tiers</ModalHeader>
        <ModalCloseButton />

        <ModalBody pb={6}>
          <VStack spacing={4} align="stretch">
            <Box>
              <Text fontSize="md" color="gray.600" mb={4}>
                This table shows all {tierType.toLowerCase()} cost share tiers
                configured for this plan design.
              </Text>

              <Text fontSize="lg" fontWeight="semibold" mb={4}>
                {tierType} Cost Share Tiers ({tierCount}{' '}
                {tierCount === 1 ? 'tier' : 'tiers'})
              </Text>
            </Box>

            <InlineEditTable
              data={tableData}
              columns={columns}
              onCellUpdate={noop}
              showAddNew={false}
              showRowActions={false}
              rowKey={(row, index) => `${index}-${row.name}`}
              tableStyles={bopTablestyles.table}
            />
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

/**
 * Simplified generator function for tier fields with modal display.
 * Returns a single field config that shows tier data in a modal table.
 */
export function generateTierFields(
  formMethodsOrData: UseFormReturn<any> | any,
  isUnbreakable: boolean,
  selectedIndex: number,
  maps: Partial<PicklistMaps>,
  isESI: boolean
): Partial<TemplateFieldConfig>[] {
  // Use the same field path as the cost share component for reactive data
  const fieldPath = `plan.plan_designs[${selectedIndex}].plan_design_details[0].cost_share_tiers`;

  // Get real-time tier data from the form using the same path as the cost share component
  let costShareTiers: any[] = [];

  if (formMethodsOrData?.watch) {
    // If we have form methods, watch the field for real-time updates
    costShareTiers = formMethodsOrData.watch(fieldPath) || [];
  } else {
    // Fallback to static data (though this shouldn't happen in practice)
    const formData = formMethodsOrData;
    const planDesigns = formData?.plan?.plan_designs ?? [];
    const planDesign = planDesigns[selectedIndex];
    const planDesignDetails = planDesign?.plan_design_details?.[0];
    costShareTiers = planDesignDetails?.cost_share_tiers || [];
  }

  // Filter tiers by pre-packaged indicator (0 = standard, 1 = unbreakable)
  const prePackagedIndFilter = isUnbreakable ? 1 : 0;
  const transformedTiers = costShareTiers.map(transformTierForDisplay);
  const tableData = filterTiersByPrePackaged(
    transformedTiers,
    prePackagedIndFilter
  );

  const tierType = isUnbreakable ? 'Unbreakable' : 'Standard';
  const tierCount = tableData.length;

  // Create the modal trigger component
  const TierModalTrigger: React.FC = () => {
    const { isOpen, onOpen, onClose } = useDisclosure();

    // Memoized columns for performance, use getCostShareColumns with readOnly=true
    const columns = useMemo(
      () => getCostShareColumns(maps, isESI, COLUMN_WIDTH, true),
      []
    );

    return (
      <>
        <Button
          variant="link"
          color="blue.600"
          onClick={onOpen}
          p={0}
          h="auto"
          minH="auto"
          fontWeight="normal"
          justifyContent="flex-start"
        >
          View {tierType} Tiers ({tierCount}{' '}
          {tierCount === 1 ? 'tier' : 'tiers'})
        </Button>

        <CostShareTableModal
          isOpen={isOpen}
          onClose={onClose}
          tierType={tierType}
          tierCount={tierCount}
          tableData={tableData}
          columns={columns}
        />
      </>
    );
  };

  return [
    {
      label: `${tierType} Cost Share Tiers`,
      name: `${tierType.toLowerCase()}_tiers_display`,
      value:
        tierCount > 0 ? (
          <TierModalTrigger />
        ) : (
          <span style={{ color: '#666', fontStyle: 'italic' }}>
            No {tierType.toLowerCase()} tiers configured
          </span>
        ),
    },
  ];
}
