// getPharmacyNetwork.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { buildFieldPath } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { nameOrder } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/ProductsAndServices/CoreProducts/PharmacyNetwork/usePharmacyNetworkFormFields';

/**
 * getPharmacyNetwork - Generates an array of TemplateFieldConfig for pharmacy network fields.
 *
 * @param formData - The organization details containing pharmacy network data.
 * @param selectedIndex - The index of the selected plan design.
 * @param pbmType - The type of PBM ('IsESI', 'IsOptum', 'IsCMK', or undefined).
 * @returns Array of field configurations for the pharmacy network section.
 */
export const getPharmacyNetwork = (
  formData: OrganizationDetails,
  selectedIndex: number
): Partial<TemplateFieldConfig>[] => {
  const productFeature = formData.features.find((feature) =>
    feature.name.includes('PharmacyNetwork')
  );

  const planDesign = formData.plan.plan_designs?.[selectedIndex];

  // Add proper array validation before calling find
  const pharmacyNetwork =
    planDesign?.plan_features && Array.isArray(planDesign.plan_features)
      ? planDesign.plan_features.find(
          (planFeature) =>
            planFeature.product_feature_id ===
            productFeature?.product_feature_id
        )
      : undefined;

  const getPicklist = (picklistId: number) => {
    const picklistValues = formData.picklists.find(
      (picklist) => picklist.picklist_id === picklistId
    )?.picklist_values;
    return picklistValues?.reduce((acc, item) => {
      acc[item.label] = item.value;
      return acc;
    }, {} as Record<string, string>);
  };

  // Helper function to find feature items by name
  const findFeatureItemsByName = (names: string[]) => {
    if (!pharmacyNetwork?.plan_feature_items) return [];

    return pharmacyNetwork.plan_feature_items.filter((feature_item) => {
      const featureItemDetail = productFeature?.feature_items.find(
        (item) => item.feature_item_id === feature_item.product_feature_item_id
      );

      if (!featureItemDetail) return false;

      return names.some(
        (name) =>
          featureItemDetail.name === name ||
          featureItemDetail.picklist_name === name ||
          featureItemDetail.label === name
      );
    });
  };

  // Helper function to generate field config for a set of feature items
  const generateFieldConfig = (featureItems: any[]) => {
    if (!featureItems) return [];
    return featureItems
      .map((feature_item, index) => {
        const featureItemDetail = productFeature?.feature_items.find(
          (item) =>
            item.feature_item_id === feature_item.product_feature_item_id
        );

        if (!featureItemDetail) return null;

        const fieldPath = buildFieldPath(feature_item, selectedIndex);
        const optionsMap = getPicklist(featureItemDetail.picklist_id || 0);
        const type =
          featureItemDetail.field_type_label === 'Text'
            ? 'input'
            : 'dropdownSelect';

        return {
          name: fieldPath,
          label: featureItemDetail.label,
          value: feature_item.value,
          optionsMap,
          type,
        };
      })
      .filter((field) => field !== null) as Partial<TemplateFieldConfig>[];
  };

  // Process each section in nameOrder and collect all fields in order
  const orderedFields: Partial<TemplateFieldConfig>[] = [];

  Object.entries(nameOrder).forEach(([sectionKey, orderedNames]) => {
    orderedNames.forEach((nameGroup) => {
      const matchingItems = findFeatureItemsByName(nameGroup);
      if (matchingItems.length > 0) {
        const fieldConfigs = generateFieldConfig(matchingItems);
        orderedFields.push(...fieldConfigs);
      }
    });
  });

  // If no fields were found in any section, fall back to the original logic
  if (orderedFields.length === 0) {
    const pharmacyNetworkFieldConfig = pharmacyNetwork?.plan_feature_items
      .map((feature_item, index) => {
        const featureItemDetail = productFeature?.feature_items.find(
          (item) =>
            item.feature_item_id === feature_item.product_feature_item_id
        );

        if (!featureItemDetail) return null;

        const fieldPath = buildFieldPath(feature_item, selectedIndex);
        const optionsMap = getPicklist(featureItemDetail.picklist_id || 0);
        const type =
          featureItemDetail.field_type_label === 'Text'
            ? 'input'
            : 'dropdownSelect';

        return {
          name: fieldPath,
          label: featureItemDetail.label,
          value: feature_item.value,
          optionsMap,
          type,
        };
      })
      .filter((field) => field !== null) as Partial<TemplateFieldConfig>[];

    return pharmacyNetworkFieldConfig || [];
  }

  return orderedFields;
};
