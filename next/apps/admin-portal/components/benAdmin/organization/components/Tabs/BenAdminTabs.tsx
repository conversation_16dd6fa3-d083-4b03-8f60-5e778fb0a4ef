import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  CLIENT_PROFILE_MODE,
  CLINICAL_DESIGN_MODE,
  MEMBER_EXPERIENCE_MODE,
  PLAN_DESIGN_MODE,
  PlanFeature,
  PRODUCTS_AND_SERVICES_MODE,
} from '../../..';
import TabSection from '../../../ReusableComponents/Components/TabSections/TabSections';
import { GenericTemplateContainer } from '../../../ReusableComponents/Components/Template/GenericTemplateContainer';
import { TabItem } from '../../../ReusableComponents/Models/types';
import {
  useIsChangeRequestPage,
  useIsIntakeOverview,
} from '../../hooks/useOrganizationHooks';
import { useConditionalValidationContext } from '../ChangeRequestIntake/Validations/useConditionalValidationContext';
import { DocumentCenter } from './DocumentCenter/DocumentCenter';
import { planDesignMap } from './PlanDesign/PlanDesign/FieldGroup/allPlanDesigns';

interface BenAdminTabsProps {
  formMethods: UseFormReturn<any>;
  clientProfile: any;
  clinicalDesign: any;
  memberExperience: any;
  planDesign: any;
  productsAndServices: any;
  internalContacts: any;
  externalContacts: any;
  planId: number;
  planFeatures: any;
  onUpdateActiveItem?: (section: string, tab?: string) => void;
  benAdminSections: {
    productsAndServicesSections: any;
    planDesignSections: any;
    memberExperienceSections: any;
    clinicalDesignSections: any;
    clientProfileSections: any;
    toggleName: string;
  };
}

/**
 * BenAdminTabs component displays the main navigation tabs for the Benefits Administration interface.
 * It conditionally renders different tabs based on the current page context (change request or overview).
 *
 * DEVELOPER NOTE: To enable percentage completion indicators:
 * 1. Always provide a formSections prop to GenericTemplateContainer
 * 2. Ensure formSections has at minimum { sections: [], id: 'section-id' }
 * 3. To keep things clean, create a custom hook like useProductsAndServicesSections()
 *
 * DEVELOPER NOTE: To enable toggle functionality for alternate views:
 * 1. Set isGuided to true
 * 2. Provide toggleObjects and toggleAccordionData props
 * 3. Make sure toggleAccordionData has a valid id and sections array
 */
const BenAdminTabs: React.FC<BenAdminTabsProps> = (props) => {
  const {
    formMethods,
    clientProfile,
    clinicalDesign,
    memberExperience,
    productsAndServices,
    internalContacts,
    externalContacts,
    planDesign,
    planId,
    onUpdateActiveItem,
    benAdminSections,
  } = props;

  const isChangeRequestPage = useIsChangeRequestPage();
  const isIntakeOverview = useIsIntakeOverview();

  const planFeatures = formMethods.getValues(
    'plan.plan_designs.0.plan_features'
  ) as PlanFeature[];

  // Move hook calls to top level
  const clientProfileValidation =
    useConditionalValidationContext(CLIENT_PROFILE_MODE);
  const memberExperienceValidation = useConditionalValidationContext(
    MEMBER_EXPERIENCE_MODE
  );
  const planDesignValidation =
    useConditionalValidationContext(PLAN_DESIGN_MODE);
  const productsAndServicesValidation = useConditionalValidationContext(
    PRODUCTS_AND_SERVICES_MODE
  );
  const clinicalDesignValidation =
    useConditionalValidationContext(CLINICAL_DESIGN_MODE);

  const isEmpty = (obj: any) => {
    return !obj || Object.keys(obj || {}).length === 0;
  };

  /**
   * Formats tab labels based on current page context
   * Removes "Hub" suffix when on intake overview pages
   */
  const formatTabLabel = (label: string): string => {
    if (isIntakeOverview && label.includes('Hub')) {
      return label.replace(' Hub', '');
    }
    return label;
  };

  /**
   * Gets error count for a given validation context
   */
  const getErrorCount = (validationContext: any) => {
    return validationContext.errorCount || 0;
  };

  // Define all available tabs with their components
  const tabData: TabItem[] = [
    {
      label: formatTabLabel('Plan Design Hub'),
      component: (
        <>
          {/* Client Profile Section
           * Required for percentage display:
           * - formSections with sections array and id
           * Now with toggle functionality
           */}
          <GenericTemplateContainer
            fieldGroups={clientProfile}
            title={'Client Profile'}
            formSections={benAdminSections.clientProfileSections}
            isGuided={isIntakeOverview}
            onUpdateActiveItem={onUpdateActiveItem}
            errorCount={getErrorCount(clientProfileValidation)}
            tabErrors={clientProfileValidation.tabErrors}
          />

          {/* Member Experience Section
           * Basic percentage display setup:
           * - formSections with empty sections array and section ID
           * Now with toggle functionality
           */}
          <GenericTemplateContainer
            fieldGroups={memberExperience}
            title={'Member Experience'}
            formSections={benAdminSections.memberExperienceSections}
            isGuided={isIntakeOverview}
            onUpdateActiveItem={onUpdateActiveItem}
            errorCount={getErrorCount(memberExperienceValidation)}
            tabErrors={memberExperienceValidation.tabErrors}
          />

          {/* Plan Design Section
           * Basic percentage display setup:
           * - formSections with empty sections array and section ID
           * Now with toggle functionality
           */}
          <GenericTemplateContainer
            fieldGroups={planDesign}
            title={'Plan Design'}
            formSections={benAdminSections.planDesignSections}
            isGuided={isIntakeOverview}
            onUpdateActiveItem={onUpdateActiveItem}
            recordIndex={true}
            dropdownMap={planDesignMap}
            disableAccordion={isEmpty(planDesignMap)}
            enableButton={true}
            errorCount={getErrorCount(planDesignValidation)}
            tabErrors={planDesignValidation.tabErrors}
          />

          {/* Products & Services Section
           * Example of advanced percentage display:
           * - Uses custom hook to generate formSections with completion tracking
           * Now with toggle functionality
           */}
          <GenericTemplateContainer
            fieldGroups={productsAndServices}
            title={'Products & Services'}
            formSections={benAdminSections.productsAndServicesSections}
            isGuided={isIntakeOverview}
            onUpdateActiveItem={onUpdateActiveItem}
            disableAccordion={isEmpty(planDesignMap)}
            errorCount={getErrorCount(productsAndServicesValidation)}
            tabErrors={productsAndServicesValidation.tabErrors}
          />

          {/* Clinical Design
           * Now with toggle functionality
           */}
          <GenericTemplateContainer
            fieldGroups={clinicalDesign}
            title={'Clinical Design'}
            formSections={benAdminSections.clinicalDesignSections}
            isGuided={isIntakeOverview}
            onUpdateActiveItem={onUpdateActiveItem}
            disableAccordion={isEmpty(planDesignMap) || isEmpty(planFeatures)}
            errorCount={getErrorCount(clinicalDesignValidation)}
            tabErrors={clinicalDesignValidation.tabErrors}
          />
        </>
      ),
    },
    {
      label: formatTabLabel('Structure Hub'),
      component: [],
    },
    {
      label: formatTabLabel('Pricing Hub'),
      component: [],
    },
    {
      label: 'Contacts',
      component: (
        <>
          {/* Note: No percentage tracking for these sections
           * To add percentage tracking, provide formSections prop
           */}
          <GenericTemplateContainer
            fieldGroups={internalContacts}
            title={'Internal RxBenefits Contacts'}
          />
          <GenericTemplateContainer
            fieldGroups={externalContacts}
            title={'External Contacts'}
          />
        </>
      ),
    },
    {
      label: 'Activity',
      component: [],
    },
    {
      label: 'Document Center',
      component: <DocumentCenter planId={planId} formMethods={formMethods} />,
    },
  ];

  // Filter tabs based on page context
  // Document Center, Activity, and Contacts tabs are excluded on change request pages
  const filteredTabData = isChangeRequestPage
    ? tabData.filter(
        (tab) =>
          tab.label !== 'Document Center' &&
          tab.label !== 'Activity' &&
          tab.label !== 'Contacts'
      )
    : tabData;

  return (
    <TabSection
      tabs={filteredTabData}
      isGuided={isIntakeOverview}
      formMethods={formMethods}
      onUpdateActiveItem={onUpdateActiveItem}
    />
  );
};

export default BenAdminTabs;
