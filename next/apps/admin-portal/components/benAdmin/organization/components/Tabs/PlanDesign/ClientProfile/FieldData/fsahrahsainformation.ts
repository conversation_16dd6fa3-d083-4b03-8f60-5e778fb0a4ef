// Example usage of the flattened FSA/HRA/HSA configuration

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import z from 'zod';

import { fsaHraHsaConfig } from '../Config/fsaHraHsaConfig';

export const getFsaHraHsaFields = (
  orgDetails: Partial<OrganizationDetails>,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planTransition = orgDetails?.plan?.plan_transition;

  return [
    {
      label: 'FSA',
      name: fsaHraHsaConfig.fsa_ind,
      value: planTransition?.fsa_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
      infoText: 'Does the group have a Flexible Spending Account?',
    },
    {
      label: 'FSA Substantiation File Needed',
      name: fsaHraHsaConfig.fsa_substantiation_needed,
      value: planTransition?.fsa_substantiation_needed,
      type: 'input',
      validations: z.string().max(255, 'Value cannot exceed 255 characters'),
      infoText:
        'If FSA is not managed through Debit Card, will a file to substantiate claims be needed? If yes, who is the vendor?',
    },
    {
      label: 'HRA',
      name: fsaHraHsaConfig.hra_ind,
      value: planTransition?.hra_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
      infoText: 'Does the group have a Health Reimbursement Account?',
    },
    {
      label: 'HRA Substantiation File Needed',
      name: fsaHraHsaConfig.hra_substantiation_needed,
      value: planTransition?.hra_substantiation_needed,
      type: 'input',
      validations: z.string().max(255, 'Value cannot exceed 255 characters'),
      infoText:
        'If HRA is not managed through Debit Card, will a file to substantiate claims be needed? If yes, who is the vendor?',
    },
    {
      label: 'HSA',
      name: fsaHraHsaConfig.hsa_ind,
      value: planTransition?.hsa_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
      infoText: 'Does the group have a Health Savings Account?',
    },
    {
      label: 'HSA Substantiation File Needed',
      name: fsaHraHsaConfig.hsa_substantiation_needed,
      value: planTransition?.hsa_substantiation_needed,
      type: 'input',
      validations: z.string().max(255, 'Value cannot exceed 255 characters'),
      infoText:
        'If HSA is not managed through Debit Card, will a file to substantiate claims be needed? If yes, who is the vendor?',
    },
  ];
};
