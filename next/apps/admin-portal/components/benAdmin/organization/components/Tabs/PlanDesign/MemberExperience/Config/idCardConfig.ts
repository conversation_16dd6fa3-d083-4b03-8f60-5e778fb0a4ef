// idCardConfig.ts

import { getPropertyPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { BASE_PATHS } from './configs';

// Define the field names without the full paths
const ID_CARD_FIELDS = {
  id_card_responsible_ind: 'id_card_responsible_ind',
  id_card_type_ind: 'id_card_type_ind',
  id_card_logo_ind: 'id_card_logo_ind',
  employee_id_source_ind: 'employee_id_source_ind',
  id_card_mailing_ind: 'id_card_mailing_ind',
  pcn: 'pcn',
  rx_bin: 'rx_bin',
  rx_grp: 'rx_grp',
  datanet_access_ind: 'datanet_access_ind',
  notes: 'notes',
};

/**
 * IdCardConfig - Type definition for the flattened ID Card configuration.
 */
export interface IdCardConfig {
  id_card_responsible_ind: string;
  id_card_type_ind: string;
  id_card_logo_ind: string;
  employee_id_source_ind: string;
  id_card_mailing_ind: string;
  pcn: string;
  rx_bin: string;
  rx_grp: string;
  datanet_access_ind: string;
  notes: string;
}

// Generate the full paths
export const idCardConfig: IdCardConfig = Object.entries(ID_CARD_FIELDS).reduce(
  (config, [key, value]) => ({
    ...config,
    [key]: getPropertyPath(BASE_PATHS.PLAN_MATERIAL, value),
  }),
  {} as IdCardConfig
);

/**
 * getIdCardPath
 * Returns a single path for the given field.
 */
export function getIdCardPath(field: keyof typeof idCardConfig): string {
  return idCardConfig[field];
}
