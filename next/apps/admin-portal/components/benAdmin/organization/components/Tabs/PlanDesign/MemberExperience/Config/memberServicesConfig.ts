// memberServicesConfig.ts

import { LegalEntityAssignments } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getPropertyPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import {
  NullableLegalEntitys,
  TemplateFieldConfig,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { BASE_PATHS, ROLE_PATHS } from './configs';

const MS_FIELDS = {
  pa_reviewer: 'pa_reviewer',
  name: 'source_legal_entity.name',
  phone: 'source_legal_entity.phones.phone_number',
};

export const ENTITY_ROLES = {
  paProvider: 'PA Provider',
  memberServices: 'Member Services',
  thirdPartyServices: 'Third Party Services',
  medicalVendor: 'Medical Vendor',
};

export const getEntityPath = (
  source: Partial<LegalEntityAssignments[]>,
  role: string,
  target: 'pa_reviewer' | 'name' | 'phone',
  tpCount?: number
) =>
  `${BASE_PATHS.LEGAL_ENTITY_ASSIGNMENT}[${getRoleIndex(
    source,
    role,
    tpCount
  )}].${MS_FIELDS[target]}`;

// Type definition for the member services configuration
export interface MemberServicesConfig {
  pa_reviewer: string;
  pa_provider_phone: string;
  member_service_name: string;
  member_service_phone: string;
  third_party_name: string;
  third_party_phone: string;
}

// Generate the full paths
export const memberServicesConfig: MemberServicesConfig = {
  // Prior Authorization fields
  pa_reviewer: getPropertyPath(
    BASE_PATHS.LEGAL_ENTITY_ASSIGNMENT,
    MS_FIELDS.pa_reviewer
  ),
  pa_provider_phone: getPropertyPath(ROLE_PATHS.PA_PROVIDER, MS_FIELDS.phone),

  // Member Services fields
  member_service_name: getPropertyPath(
    ROLE_PATHS.MEMBER_SERVICES,
    MS_FIELDS.name
  ),
  member_service_phone: getPropertyPath(
    ROLE_PATHS.MEMBER_SERVICES,
    MS_FIELDS.phone
  ),

  // Third Party Services fields
  third_party_name: getPropertyPath(
    ROLE_PATHS.THIRD_PARTY_SERVICES,
    MS_FIELDS.name
  ),
  third_party_phone: getPropertyPath(
    ROLE_PATHS.THIRD_PARTY_SERVICES,
    MS_FIELDS.phone
  ),
};

/**
 * Interface for entity data with role and source legal entity information
 */
export interface AssignmentData {
  name: string;
  phone: string;
}

export interface EntityAssignments {
  planDesignId: number | null;
  paReviewer: AssignmentData;
  memberService: AssignmentData;
  medicalVendor: AssignmentData;
  thirdPartyServices: AssignmentData[];
  tpCount: number;
  paAvailability: boolean;
  msAvailability: boolean;
  medAvailability: boolean;
}

/**
 * Extract plan design data and related entities from form data
 * @param formData The form data object
 * @returns An array of objects containing plan design and relevant entity data
 */
export function extractPlanDesignData(formData: any): EntityAssignments[] {
  const result: EntityAssignments[] = [];

  const entityAssignments = formData?.plan?.legal_entity_assignment || [];

  if (entityAssignments.length === 0) return [];

  entityAssignments.forEach((entity: any) => {
    let idx = result.findIndex(
      (obj) => obj?.planDesignId === entity?.target_plan_design_id
    );

    if (idx === -1) {
      result.push({
        planDesignId: entity?.target_plan_design_id,
        paReviewer: getEmptyAssignmentData(),
        memberService: getEmptyAssignmentData(),
        medicalVendor: getEmptyAssignmentData(),
        thirdPartyServices: getEmptyAssignmentData('tps'),
        tpCount: -1,
        paAvailability: false,
        msAvailability: false,
        medAvailability: false,
      });
      idx = result?.length - 1;
    }
    switch (entity?.role?.name) {
      case 'PA Provider':
        {
          result[idx].paAvailability = true;
          result[idx].paReviewer.name = entity?.pa_reviewer;
          result[idx].paReviewer.phone =
            entity?.source_legal_entity?.phones?.phone_number;
        }
        break;
      case 'Member Services':
        {
          result[idx].msAvailability = true;
          result[idx].memberService.name = entity?.source_legal_entity?.name;
          result[idx].memberService.phone =
            entity?.source_legal_entity?.phones?.phone_number;
        }
        break;
      case 'Medical Vendor':
        {
          result[idx].medAvailability = true;
          result[idx].medicalVendor.name = entity?.source_legal_entity?.name;
        }
        break;
      case 'Third Party Services': {
        result[idx].tpCount = result[idx].tpCount + 1;
        result[idx].thirdPartyServices[result[idx].tpCount].name =
          entity?.source_legal_entity?.name;
        result[idx].thirdPartyServices[result[idx].tpCount].phone =
          entity?.source_legal_entity?.phones?.phone_number;
      }
    }
  });
  return result;
}

export function getMemberServices(
  data: EntityAssignments
): Partial<TemplateFieldConfig>[] {
  return [
    {
      label: 'General Member Service Vendor',
      value: data?.memberService?.name,
      name: memberServicesConfig.member_service_name,
      type: 'input' as const,
      placeholder: 'Enter General Member Service Vendor',
      infoText: 'Who members call for Rx questions.',
      validations: z
        .string()
        .max(50, 'Name cannot exceed 50 characters')
        .optional(),
    },
    {
      label: 'General Member Service Phone Number',
      value: data?.memberService?.phone,
      name: memberServicesConfig.member_service_phone,
      type: 'input' as const,
      placeholder: 'Enter General Member Service Phone',
      validations: z
        .string()
        .regex(/^\d{10}$/, 'Phone number must be 10 numeric digits only.')
        .optional(),
    },
  ];
}

export function getTPS(
  data: EntityAssignments
): Partial<TemplateFieldConfig>[] {
  const tps: Partial<TemplateFieldConfig>[] = [];
  for (let i = -1; i < 3; i++) {
    tps.push(
      {
        label: `Third Party Service ${i + 2}`,
        value: data?.thirdPartyServices?.[i + 1]?.name,
        name: `${memberServicesConfig.third_party_name}-${i + 2}`,
        type: 'input' as const,
        placeholder: `Enter Third Party Service ${i + 2}`,
        infoText:
          i === -1
            ? 'Additional vendor(s) with whom the client has a relationship.'
            : i === 0
            ? 'Additional vendor(s) with whom the client has a relationship.'
            : i === 1
            ? 'Additional vendor(s) with whom the client has a relationship'
            : 'Additional vendor(s) with whom the client has a relationship.',
        validations: z
          .string()
          .max(50, 'Name cannot exceed 50 characters')
          .optional(),
      },
      {
        label: `Third Party Service ${i + 2} Phone Number`,
        value: data?.thirdPartyServices?.[i + 1]?.phone,
        name: `${memberServicesConfig.third_party_phone}-${i + 2}`,
        type: 'input' as const,
        placeholder: `Enter Third Party Service ${i + 2} Phone`,
        validations: z
          .string()
          .regex(/^\d{10}$/, 'Phone number must be 10 numeric digits only.')
          .optional(),
      }
    );
  }
  return tps;
}

const getEmptyAssignmentData = (field?: string): any => {
  const values = { name: '', phone: '' };

  if (field === 'tps') {
    const tpsFields = [];
    for (let i = 0; i < 4; i++) tpsFields.push({ name: '', phone: '' });
    return tpsFields;
  }

  return values;
};

export const getEmptyLegalEntityData = (
  planDesignId: number | null,
  roles: string[],
  tpsCount: number
): NullableLegalEntitys[] => {
  const result = [];
  if (tpsCount > 0) {
    while (tpsCount) {
      roles.push('Third Party Services');
      tpsCount--;
    }
  }

  for (let i = 0; i < roles?.length; i++) {
    result.push({
      legal_entity_assignment_id: null,
      legal_entity_id: null,
      pa_reviewer: '',
      role_id: null,
      target_legal_entity_id: null,
      target_plan_id: null,
      target_plan_design_id: planDesignId,
      effective_date: '',
      expiration_date: '',
      created_date: '',
      created_by: null,
      updated_date: '',
      updated_by: null,
      version_number: null,
      role: {
        role_id: null,
        entity_scope: null,
        role_type: null,
        name: `${roles[i]}`,
        created_date: '',
        created_by: null,
        updated_date: '',
        updated_by: null,
        version_number: null,
        allow_multiple_ind: roles[i] === 'Medical Vendor' ? 1 : 0,
      },
      source_legal_entity: {
        legal_entity_id: null,
        name: '',
        registration_number: null,
        created_date: '',
        created_by: null,
        updated_date: '',
        updated_by: null,
        version_number: null,
        phones: {
          phone_id: null,
          owner_id: null,
          owner_type: '',
          phone_number: '',
          phone_type: '',
          is_primary: false,
          created_date: '',
          created_by: null,
          updated_date: '',
          updated_by: null,
          version_number: null,
        },
      },
    });
  }

  return result;
};

export const getRoleIndex = (
  result: Partial<LegalEntityAssignments[]>,
  property: string,
  tpCount?: number
) => {
  let index;

  index = result?.findIndex((data) => data?.role?.name === property);
  if (property === 'Third Party Services' && tpCount !== undefined)
    index = index + tpCount;
  return index;
};
