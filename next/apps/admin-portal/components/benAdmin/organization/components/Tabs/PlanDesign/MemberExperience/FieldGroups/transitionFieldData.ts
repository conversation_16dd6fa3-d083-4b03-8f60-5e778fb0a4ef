// getTransitionFields.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { transitionConfig } from '../Config/transitionConfig'; // Adjust path as needed

const percentValidation = z
  .string()
  .regex(/^(\d{1,3})(\.\d{1,2})?$/, {
    message: 'Must be a valid percentage with up to 2 decimal places',
  })
  .refine((value) => parseFloat(value) <= 100, {
    message: 'Percentage cannot exceed 100',
  })
  .transform((val) => parseFloat(val));

/**
 * getTransitionFields - Generates an array of TemplateFieldConfig for transition fields.
 *
 * It uses a centralized configuration (transitionConfig) for the dot-notated field names.
 *
 * @param formData - The organization details containing transition data.
 * @returns Array of field configurations for the transition section.
 */
export const getTransitionFields = (
  formData: OrganizationDetails,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planTransition = formData?.plan?.plan_transition;
  const productName = formData?.plan?.product?.name;
  return [
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Historical Claims File',
            value: planTransition?.historical_claims_ind,
            name: transitionConfig.historical_claims_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.historicalClaimsMap,
            placeholder: 'Select History Claim',
            infoText:
              'Specifies if a historical claims file will be obtained for this client.',
          },
          {
            label: 'ORT Transition File',
            value: planTransition?.ort_transition_file_ind,
            name: transitionConfig.ort_transition_file_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.transitionFileMap,
            placeholder: 'Select ORT Transition File',
            infoText:
              'Specifies if an Open Refill Transfer file will be obtained (transfers mail order refills to the new PBM mail order pharmacy).',
          },
          {
            label: 'Mail Order Percentage',
            value: planTransition?.mail_order_percentage,
            name: transitionConfig.mail_order_percentage,
            type: 'input' as const,
            placeholder: 'Enter Mail Order Percentage',
            infoText:
              'Estimated percentage of members currently using mail order/home delivery.',
            validations: percentValidation.optional(),
          },
          {
            label: 'PA File',
            value: planTransition?.pa_file_ind,
            name: transitionConfig.pa_file_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.transitionFileMap,
            placeholder: 'Select PA File',
            infoText:
              'Whether or not a Prior Authorization file will be obtained for this client.',
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Outbound Claims Files',
            value: planTransition?.outbound_claims_file,
            name: transitionConfig.outbound_claims_file,
            type: 'input' as const,
            placeholder: 'Enter Outbound Claims Files',
            infoText:
              'Do claims files need to be sent to another vendor, and if so, what vendor?',
            validations: z
              .string()
              .max(255, 'Value cannot exceed 255 characters'),
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Carrier to Carrier',
            value: planTransition?.carrier_to_carrier_ind,
            name: transitionConfig.carrier_to_carrier_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.carrierToCarrierMap,
            placeholder: 'Select Carrier Mapping',
            infoText:
              'Moves existing Caremark clients to the RxB/CMK arrangement .',
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Accum Priming Balance File',
            value: planTransition?.accum_priming_balance_file_ind,
            name: transitionConfig.accum_priming_balance_file_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Accum Priming Balance File',
          },
          {
            label: 'Include Weight Loss Drug in PA file',
            value: planTransition?.include_weight_loss_drug_ind,
            name: transitionConfig.include_weight_loss_drug_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
            placeholder: 'Select Yes/No',
            infoText: 'Do you want to include Weight Loss Drug in PA file ?',
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Transition Notes',
            value: planTransition?.notes,
            name: transitionConfig.notes,
            type: 'textarea' as const,
            placeholder: 'Enter your notes',
            validations: z
              .string()
              .max(2000, 'Value cannot exceed 2000 characters'),
          },
        ]
      : []),
    ...([
      productNames.ESI_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Accum Transfer',
            value: planTransition?.accum_transfer_ind,
            name: transitionConfig.accum_transfer_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.accumTransferMap,
            placeholder: 'Select Accum Transfer',
            infoText:
              'Specifies whether accums transfer happens at ESI at the individual contract level, or the whole carrier level (rarely used).',
          },
        ]
      : []),
  ];
};
