import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';
import { z } from 'zod';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { getOtherPAPath } from '../Config/otherPAConfig';

// Validation schemas
const currencyValidation = z
  .string()
  .regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid currency amount')
  .optional();

export function getOtherPAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];

  const otherCapTemplates: TemplateFieldGroup[] = [];

  const productName = planData?.plan?.product?.name;

  // Use map with early return pattern, then filter out null values
  planDesignDetails.forEach((designDetail) => {
    const planDesignDetailsAccumOther = designDetail?.accumulation_other;

    // Skip plans without accumulation_other details by returning null
    if (!planDesignDetailsAccumOther) {
      return null;
    }

    planDesignDetailsAccumOther.forEach((otherCap, index) => {
      otherCapTemplates.push({
        subtitle: `Pharmacy Accumulator - Other Cap - ${
          otherCap.accums_tier_name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          ...([
            productNames.CMK_360,
            productNames.ESI_360,
            productNames.OPT_360,
            productNames.IRX_360,
          ].includes(productName as productNames)
            ? [
                {
                  label: 'Accums Tier Type',
                  value: otherCap.other_accum_type_ind,
                  name: getOtherPAPath('other_accum_type_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.otherAccumTypeMap,
                  infoText: 'Accums Tier Type',
                },
                {
                  label: 'Accums Tier Name',
                  value: otherCap?.accums_tier_name,
                  name: getOtherPAPath('accums_tier_name', index),
                  type: 'input' as const,
                  placeholder: 'Enter Accums Tier Name',
                  infoText: 'Accums Tier Name',
                  validations: z
                    .string()
                    .max(
                      255,
                      'Accums Tier Name must be less than 255 characters'
                    )
                    .optional()
                    .nullable(),
                },
                {
                  label: 'Accums Tier Effective Date',
                  value: otherCap?.effective_date,
                  name: getOtherPAPath('effective_date', index),
                  type: 'datepicker' as const,
                  infoText: 'Choose Accums Tier Effective Date',
                  placeholder: 'Select Accums Tier Effective Date',
                },
                {
                  label: 'Accums Tier End Date',
                  value: otherCap?.expiration_date,
                  name: getOtherPAPath('expiration_date', index),
                  type: 'datepicker' as const,
                  infoText: 'Choose Accums Tier End Date',
                  placeholder: 'Select Accums Tier End Date',
                },
                {
                  label: 'Accums Tier PBC order',
                  value: otherCap?.pbc_order,
                  name: getOtherPAPath('pbc_order', index),
                  type: 'input' as const,
                  placeholder: 'Enter Accums Tier PBC order',
                  infoText: 'Enter Accums Tier PBC order',
                },
                {
                  label: 'Accumulation Period',
                  value: otherCap?.accum_period_ind,
                  name: getOtherPAPath('accum_period_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.esiAccumPeriodMap,
                  infoText: 'Choose Accumulation Period',
                },
                {
                  label: 'Specify Other Cap Accumulation Period',
                  value: otherCap?.specify_accum_period_ind,
                  name: getOtherPAPath('specify_accum_period_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.benefitPeriodsMap,
                  infoText: 'Choose Specify Other Cap Accumulation Period',
                },
              ]
            : []),
          ...(productName === productNames.ESI_360
            ? [
                {
                  label: 'Benefit Period Length',
                  value: otherCap?.benefit_period_length_ind,
                  name: getOtherPAPath('benefit_period_length_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.benefitPeriodLengthMap,
                  infoText: 'Choose Benefit Period Length',
                },
                {
                  label: 'Benefit Period Length - Other',
                  value: otherCap?.benefit_period_length_other,
                  name: getOtherPAPath('benefit_period_length_other', index),
                  type: 'input' as const,
                  placeholder: 'Benefit Period Length - Other',
                  infoText: 'Benefit Period Length - Other',
                  validations: z
                    .number()
                    .int()
                    .nullable()
                    .optional()
                    .refine((val) => val == null || val > 0, {
                      message: 'Number must be greater than 0',
                    }),
                },
              ]
            : []),
          ...([
            productNames.CMK_360,
            productNames.ESI_360,
            productNames.OPT_360,
            productNames.IRX_360,
          ].includes(productName as productNames)
            ? [
                {
                  label: 'Do Priming Balances Apply?',
                  value: otherCap?.priming_balances_ind,
                  name: getOtherPAPath('priming_balances_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                  infoText: 'Do Priming Balances Apply?',
                },
                {
                  label: 'Carryover Phase',
                  value: otherCap?.carryover_phase_ind,
                  name: getOtherPAPath('carryover_phase_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.carryoverPhaseMap,
                  infoText: 'Choose Carryover Phase',
                },
                {
                  label: 'Describe Carryover Phase',
                  value: otherCap?.describe_carryover_phase,
                  name: getOtherPAPath('describe_carryover_phase', index),
                  type: 'input' as const,
                  placeholder: 'Enter Describe Carryover Phase',
                  infoText: 'Enter Describe Carryover Phase',
                },
                {
                  label: 'Individual Cap Amount',
                  value: otherCap?.individual_plan_amount,
                  name: getOtherPAPath('individual_plan_amount', index),
                  type: 'input' as const,
                  placeholder: 'Enter Individual Cap Amount',
                  infoText: 'Enter Individual Cap Amount',
                  validations: currencyValidation,
                },
                {
                  label: 'Family Cap Amount',
                  value: otherCap?.family_plan_amount,
                  name: getOtherPAPath('family_plan_amount', index),
                  type: 'input' as const,
                  placeholder: 'Enter Family Cap Amount',
                  infoText: 'Enter Family Cap Amount',
                  validations: currencyValidation,
                },
                {
                  label: 'Employee +1 Cap Amount',
                  value: otherCap?.employee_1_dep_amount,
                  name: getOtherPAPath('employee_1_dep_amount', index),
                  type: 'input' as const,
                  placeholder: 'Enter Employee +1 Cap Amount',
                  infoText: 'Enter Employee +1 Cap Amount',
                  validations: currencyValidation,
                },
                {
                  label: 'Individual within Family Cap Amount',
                  value: otherCap?.individual_within_family_amount,
                  name: getOtherPAPath(
                    'individual_within_family_amount',
                    index
                  ),
                  type: 'input' as const,
                  placeholder: 'Enter Individual within Family Cap Amount',
                  infoText: 'Enter Individual within Family Cap Amount',
                  validations: currencyValidation,
                },
                {
                  label: 'Max Allowable Cap',
                  value: otherCap?.max_allowable_cap,
                  name: getOtherPAPath('max_allowable_cap', index),
                  type: 'input' as const,
                  placeholder: 'Enter Max Allowable Cap',
                  infoText: 'Enter Max Allowable Cap',
                  validations: currencyValidation,
                },
              ]
            : []),
          ...(productName === productNames.ESI_360
            ? [
                {
                  label: 'CDH Class Code',
                  value: otherCap?.cdh_class_code_ind,
                  name: getOtherPAPath('cdh_class_code_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.cdhClassCodeMap,
                  infoText: 'Choose CDH Class Code',
                },
                {
                  label: 'Shared Indicator',
                  value: otherCap?.shared_ind,
                  name: getOtherPAPath('shared_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.sharedIndicatorMap,
                  infoText: 'Choose Shared Indicator',
                },
                {
                  label: 'Drug Type Status',
                  value: otherCap?.drug_type_status_ind,
                  name: getOtherPAPath('drug_type_status_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.drugTypeStatusMap,
                  infoText: 'Choose Drug Type Status',
                },
                {
                  label: 'Formulary Status',
                  value: otherCap?.formulary_status_ind,
                  name: getOtherPAPath('formulary_status_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.formularyStatusMap,
                  infoText: 'Choose Formulary Status',
                },
              ]
            : []),
          ...([
            productNames.CMK_360,
            productNames.ESI_360,
            productNames.OPT_360,
            productNames.IRX_360,
          ].includes(productName as productNames)
            ? [
                {
                  label: 'Network Status',
                  value: otherCap?.network_status_ind,
                  name: getOtherPAPath('network_status_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.networkStatusMap || {},
                },
              ]
            : []),
          ...(productName === productNames.ESI_360
            ? [
                {
                  label: 'Pharmacy Channel',
                  value: otherCap?.pharmacy_channel_ind,
                  name: getOtherPAPath('pharmacy_channel_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.pharmacyChannelAccumMap,
                },
                {
                  label: 'Network Applicability',
                  value: otherCap?.network_applicability_ind,
                  name: getOtherPAPath('network_applicability_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.networkApplicabilityMap || {},
                },
                {
                  label: 'Include Drug List',
                  value: otherCap?.include_drug_list_ind,
                  name: getOtherPAPath('include_drug_list_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.accumDrugListMap,
                  infoText: 'Choose Include Drug List',
                },
                {
                  label: 'Exclude Drug List',
                  value: otherCap?.exclude_drug_list_ind,
                  name: getOtherPAPath('exclude_drug_list_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.accumDrugListMap,
                  infoText: 'Choose Exclude Drug List',
                },
                {
                  label: 'Other Cap Notes',
                  value: otherCap?.notes,
                  name: getOtherPAPath('notes', index),
                  type: 'textarea' as const,
                  placeholder: 'Enter Other Cap Notes',
                  infoText: 'Other Cap Notes',
                  validations: z
                    .string()
                    .max(2000, 'Value must be less than 2000 characters')
                    .optional()
                    .nullable(),
                  rows: 5,
                  customProps: {
                    minHeight: '120px',
                    overflow: 'hidden',
                  },
                },
              ]
            : []),
        ],
      });
    });
  });
  return otherCapTemplates.filter(Boolean) as TemplateFieldGroup[];
}
