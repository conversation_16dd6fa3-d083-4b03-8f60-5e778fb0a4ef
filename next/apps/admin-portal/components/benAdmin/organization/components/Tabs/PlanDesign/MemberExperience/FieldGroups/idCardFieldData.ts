// getIdCardFields.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import z from 'zod';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { idCardConfig } from '../Config/idCardConfig';

export const getIdCardFields = (
  formData: OrganizationDetails,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  // Extract plan_material from formData
  const planMaterial = formData?.plan?.plan_material;
  const productName = formData?.plan?.product?.name;
  return [
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Responsible for ID Cards',
            value: planMaterial?.id_card_responsible_ind,
            name: idCardConfig.id_card_responsible_ind, // Uses the flattened config
            type: 'dropdownSelect' as const,
            optionsMap: maps.idCardsResponsibleMap,
            placeholder: 'Select Responsible for ID Cards',
            infoText: 'Designates which entity will print member ID cards.',
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'ID Card Type',
            value: planMaterial?.id_card_type_ind,
            name: idCardConfig.id_card_type_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.idCardTypeMap,
            placeholder: 'Select ID Card Type',
            infoText:
              'Specifies whether ID card has Rx information only (separate), or both Rx and Medical (combined).',
          },

          {
            label: 'Logo on ID Cards',
            value: planMaterial?.id_card_logo_ind,
            name: idCardConfig.id_card_logo_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoNaMap,
            placeholder: 'Select Logo Applicability',
            infoText:
              "Whether the client's logo will be printed on the ID card. Used only when ID Card Type is 'Separate'.",
          },
          {
            label: 'Extract Alternate ID',
            value: planMaterial?.employee_id_source_ind,
            name: idCardConfig.employee_id_source_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.employeeIdSourceMap,
            placeholder: 'Select Alternate Id',
            infoText: 'Generated ID cannot be used for combined card type.',
          },
          {
            label: 'ID Card Mailing',
            value: planMaterial?.id_card_mailing_ind,
            name: idCardConfig.id_card_mailing_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.idCardMailingMap,
            placeholder: 'Select ID Card Mailing',
            infoText:
              "Whether ID cards will be mailed to members' homes directly, or sent to the client's HR office.",
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'PCN',
            value: planMaterial?.pcn,
            name: idCardConfig.pcn,
            type: 'input' as const,
            placeholder: 'Enter PCN',
            validations: z
              .string()
              .max(10, 'PCN must not exceed 10 characters'),
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Rx BIN',
            value: planMaterial?.rx_bin,
            name: idCardConfig.rx_bin,
            type: 'input' as const,
            placeholder: 'Enter Rx BIN',
            validations: z
              .string()
              .regex(/^\d+$/, 'Value must be a number')
              .max(10, 'Rx BIN must not exceed 10 digits'),
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Rx GRP',
            value: planMaterial?.rx_grp,
            name: idCardConfig.rx_grp,
            type: 'input' as const,
            placeholder: 'Enter Rx GRP',
            validations: z.string().max(10, 'Rx GRP must not exceed 10 digits'), // validations ??
          },
          {
            label: 'DataNet Access',
            value: planMaterial?.datanet_access_ind,
            name: idCardConfig.datanet_access_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
            infoText: 'DataNet Access',
            placeholder: 'Select DataNet Access',
          },
        ]
      : []),
    {
      label: 'ID Cards & Member Materials Note',
      value: planMaterial?.notes,
      name: idCardConfig.notes,
    },
  ];
};
