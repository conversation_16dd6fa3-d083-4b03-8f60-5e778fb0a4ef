import {
  generatePaths,
  replacePlaceholder,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { PLAN_DESIGN_DETAILS_BASE_PATH } from './coreConfig';

// Base path for ESI plan design details
export const PLAN_DESIGN_ESI_BASE_PATH = `${PLAN_DESIGN_DETAILS_BASE_PATH}.plan_design_detail_esi[0]`;

// Define field names relative to the ESI base path
export const XML_FIELDS = {
  shared_vendor_id: 'shared_vendor_id',
  network_applicability_ind: 'network_applicability_ind',
  vendor_policy_number: 'vendor_policy_number',
  spending_account_type_ind: 'spending_account_type_ind',
  xml_accums_complete_ind: 'xml_accums_complete_ind',
  hra_members_access_ind: 'hra_members_access_ind',
  hsa_admin_phone: 'hsa_admin_phone',
  hsa_medical_phone: 'hsa_medical_phone',
  claim_submission_type_ind: 'claim_submission_type_ind',
  insulin_method_ind: 'insulin_method_ind',
};

/**
 * Type definition for the xml paths.
 */
export interface XmlPaths extends Record<string, string> {
  shared_vendor_id: string;
  network_applicability_ind: string;
  vendor_policy_number: string;
  spending_account_type_ind: string;
  xml_accums_complete_ind: string;
  hra_members_access_ind: string;
  hsa_admin_phone: string;
  hsa_medical_phone: string;
}

const xmlConfig: XmlPaths = {
  shared_vendor_id: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.shared_vendor_id}`,
  network_applicability_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.network_applicability_ind}`,
  vendor_policy_number: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.vendor_policy_number}`,
  spending_account_type_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.spending_account_type_ind}`,
  xml_accums_complete_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.xml_accums_complete_ind}`,
  hra_members_access_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.hra_members_access_ind}`,
  hsa_admin_phone: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.hsa_admin_phone}`,
  hsa_medical_phone: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.hsa_medical_phone}`,
  claim_submission_type_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.claim_submission_type_ind}`,
  insulin_method_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.insulin_method_ind}`,
};

export function getXmlPath(field: keyof XmlPaths, index: number): string {
  return replacePlaceholder(xmlConfig[field], index);
}

export function getXmlPaths(index: number): XmlPaths {
  return generatePaths(xmlConfig as Record<string, string>, index) as XmlPaths;
}
