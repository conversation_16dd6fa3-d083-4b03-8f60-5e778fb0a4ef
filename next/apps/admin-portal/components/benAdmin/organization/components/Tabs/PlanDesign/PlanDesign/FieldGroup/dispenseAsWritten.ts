import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { DispenseAsWrittenOption } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/PlanDesign/PatientPay/Dispense/dispenseForm';
import { getDispenseAsWrittenPaths } from '../Config/dispensesWrittenConfig';

export const getDispenseAsWrittenFeilds = (
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  dawPicklist: any
): Partial<TemplateFieldConfig>[] => {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const dispenseOptions = (dawPicklist?.DispenseAsWritten || []).reduce(
    (acc: Record<string, string>, item: DispenseAsWrittenOption) => {
      acc[item.value] = item.label;
      return acc;
    },
    {}
  );

  const dispenseAsWrittenConfig = getDispenseAsWrittenPaths(selectedIndex);

  return [
    {
      label: 'Dispense as Written',
      name: dispenseAsWrittenConfig.dispense_as_written_ind,
      value: planDesign.plan_design_details?.[0]?.dispense_as_written_ind,
      optionsMap: dispenseOptions,
      type: 'dropdownSelect' as const,
      isRequired: true,
    },
    {
      label: 'Dispense as Written Description',
      name: dispenseAsWrittenConfig.dispense_as_written_description,
      value:
        planDesign.plan_design_details?.[0]?.dispense_as_written_description,
      type: 'textarea' as const,
      infoText:
        'Which copay structure is used and whether a penalty will apply, based on the Rx DAW code as specified by the prescriber.',
      placeholder: 'Enter Description',
      validations: z.string().max(255, 'Value cannot exceed 255 characters'),
    },
  ];
};
