// Example usage of the flattened PBM information configuration

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import z from 'zod';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { getMedicalVendorField, pbmConfig } from '../Config/pbmConfig';

export const getPBMInformationFields = (
  planDetails: Partial<OrganizationDetails>,
  maps?: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const productName = planDetails?.plan?.product?.name;
  const medicalVendorField = getMedicalVendorField(planDetails);

  return [
    {
      label: 'Chosen PBM',
      name: pbmConfig.vendor_name,
      value: planDetails?.plan?.product?.vendor?.name,
      type: 'input' as const,
      placeholder: 'Select an Option',
      validations: z.string().max(255, 'Value cannot exceed 255 characters'),
    },
    ...(productName === productNames.CMK_360 ||
    productName === productNames.ESI_360 ||
    productName === productNames.OPT_360 ||
    productName === productNames.IRX_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          {
            ...medicalVendorField,
            optionsMap: maps?.medicalVendorMap,
          },
        ]
      : []),
    {
      label: 'Carrier Number',
      value: planDetails?.plan?.carrier_number,
      name: pbmConfig.carrier_number,
      type: 'input' as const,
      placeholder: 'Enter Carrier Number',
      validations: z
        .string({ required_error: 'Carrier Number is required.' })
        .max(100, 'Carrier Number cannot exceed 100 characters'),
    },
    ...(productName !== productNames.ESI_360
      ? [
          {
            label: 'Account Number',
            value: planDetails?.plan?.account_number,
            name: pbmConfig.account_number,
            type: 'input' as const,
            isRequired: true,
            infoText:
              "Usually an abbreviation of the client's name. Account number used for OPT and CMK clients only.",
            placeholder: 'Enter Account Number',
            validations: z
              .string({
                required_error: 'Account Number is required for Optum/CVS.',
              })
              .max(15, 'Account Number cannot exceed 100 characters'),
          },
        ]
      : []),
    ...(productName === productNames.ESI_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          {
            label: 'Contract Name',
            value: planDetails?.plan?.contract_name,
            name: pbmConfig.contract_name,
            type: 'input' as const,
            placeholder: 'Enter Contract Name',
            validations: z
              .string()
              .max(100, 'Contract Name cannot exceed 100 characters'),
          },
          {
            label: 'Paid Contract Number',
            value: planDetails?.plan?.paid_contract_number,
            name: pbmConfig.paid_contract_number,
            type: 'input' as const,
            isRequired: true,
            infoText:
              '8 digits long and normally RXB + abbreviation of the client name. Ex: RXBAMUNE for Amuneal Manufacturing Corporation',
            placeholder: 'Enter Paid Contract Number',
            validations: z
              .string({
                required_error: 'Paid Contract Number is required for ESI.',
              })
              .min(1, 'Paid Contract Number cannot be empty for ESI.')
              .max(100, 'Value cannot exceed 100 characters'),
          },
          {
            label: 'Umbrella Number',
            value: planDetails?.plan?.group_umbrella_number,
            name: pbmConfig.group_umbrella_number,
            type: 'input' as const,
            isRequired: true,
            infoText:
              'Umbrella Group number is shown on the ID card. The Umbrella Group number is the Paid Contract Number, minus the last letter',
            placeholder: 'Enter Group Umbrella Number',
            validations: z
              .string({
                required_error: 'Group Umbrella Number is required for ESI.',
              })
              .min(1, 'Group Umbrella Number cannot be empty for ESI.')
              .max(100, 'Value cannot exceed 100 characters'),
          },
        ]
      : []),
  ];
};
