// Example usage of the flattened eligibility configuration

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { eligibilityConfig } from '../Config/eligibilityConfig';
export const getEligibilityFields = (
  orgDetails: Partial<OrganizationDetails>,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planEligibility = orgDetails?.plan?.plan_eligibility;
  const planPDX = orgDetails?.plan?.plan_pdx;
  const productName = orgDetails?.plan?.product?.name;

  return [
    {
      label: 'COBRA',
      name: eligibilityConfig.cobra_ind,
      value: planEligibility?.cobra_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Active Employees',
      name: eligibilityConfig.active_employees_ind,
      value: planEligibility?.active_employees_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Retirees',
      name: eligibilityConfig.retirees_ind,
      value: planEligibility?.retirees_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Retiree Subsidy',
      name: eligibilityConfig.retiree_subsidy_ind,
      value: planEligibility?.retiree_subsidy_ind,
      type: 'input',
    },
    {
      label: 'Retiree Group Trust',
      name: eligibilityConfig.retirees_trust_ind,
      value: planPDX?.retirees_trust_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    ...([
      productNames.ESI_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Dependent Age',
            name: eligibilityConfig.dependent_age,
            value: planEligibility?.dependent_age,
          },

          {
            label: 'Dependent Age Indicator',
            name: eligibilityConfig.dependent_age_ind,
            value: planEligibility?.dependent_age_ind
              ? planEligibility?.dependent_age_ind
              : 3,
            type: 'dropdownSelect' as const,
            optionsMap: maps.studentAgeIndicatorMap,
          },
          {
            label: 'Student Age',
            name: eligibilityConfig.student_age,
            value: planEligibility?.student_age,
          },
          {
            label: 'Student Age Indicator',
            name: eligibilityConfig.student_age_ind,
            value: planEligibility?.student_age_ind
              ? planEligibility?.student_age_ind
              : 4,
            type: 'dropdownSelect' as const,
            optionsMap: maps.studentAgeIndicatorMap,
          },
        ]
      : []),

    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Dependent Alternate Groups',
            name: eligibilityConfig.dependent_alt_groups_ind,
            value: planEligibility?.dependent_alt_groups_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
          },
        ]
      : []),
  ];
};
