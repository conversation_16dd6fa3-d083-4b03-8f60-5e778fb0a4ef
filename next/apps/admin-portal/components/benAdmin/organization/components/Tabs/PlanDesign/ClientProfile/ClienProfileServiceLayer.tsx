// import { createDynamicGroups } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { UseFormReturn } from 'react-hook-form';

import { usePicklistMaps } from '../../../../maps/picklistMaps';
import { productNames } from '../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import {
  CLAIMS_COVER_ITEM,
  CLIENT_INFORMATION_ITEM,
  ELIGIBILITY_ITEM,
  FSA_HRA_HSA_ITEM,
  IMPLEMENTATION_ITEM,
  //   IN_HOUSE_PHARMACY_ITEM,
  PHARMACY_BENEFITS_MANAGER_ITEM,
} from '../../../ChangeRequestIntake/Navigation/navigationConstants';
import { getClaimsCoverFields } from './FieldData/claimsCoverData';
import { getClientInformationFields } from './FieldData/clientInformationFieldData';
import { getEligibilityFields } from './FieldData/eligibilityFieldData';
import { getFsaHraHsaFields } from './FieldData/fsahrahsainformation';
import { getImplementationInformationFields } from './FieldData/implemenationInformationFieldData';
// import { getInHousePharmacyInformationFields } from './FieldData/inHouseInformationFieldData';
import { getPBMInformationFields } from './FieldData/pbmInformationFieldData';

export function useClientInformationFieldGroup(
  orgName: string,
  formMethods: UseFormReturn<any> // Accept the form instance from OrganizationView
) {
  const { register, handleSubmit, watch } = formMethods;

  const maps = usePicklistMaps();

  // Get an array of pharmacy groups from your helper.
  //   const pharmacyGroups = getInHousePharmacyInformationFields(
  //     watch(),
  //     handleSubmit,
  //     register,
  //     formMethods,
  //     maps
  //   );

  //   const dynamicPharmacyGroups = createDynamicGroups(
  //     pharmacyGroups,
  //     'inHousePharmacyInformationGroup',
  //     (group) => {
  //       return {
  //         ...group,
  //         subtitle: `${group.subtitle}`,
  //         tab: IN_HOUSE_PHARMACY_ITEM, // Replaced 'in-house-pharmacy'
  //       };
  //     }
  //   );

  const fields = getPBMInformationFields(watch(), maps);

  const productName = watch('plan.product.name');
  return {
    clientProfile: {
      clientInformationGroup: {
        subtitle: 'Client Information',
        fields: getClientInformationFields(watch(), orgName, maps),
        columns: 2,
        handleSubmit,
        register,
        editable: true,
        formMethods,
        tab: CLIENT_INFORMATION_ITEM,
      },
      pbmInformationGroup: {
        subtitle: 'Pharmacy Benefits Manager',
        fields: fields,
        columns: 2,
        handleSubmit,
        register,
        editable: true,
        formMethods,
        tab: PHARMACY_BENEFITS_MANAGER_ITEM,
      },
      // Spread the dynamically created pharmacy groups into the returned object.
      //   ...dynamicPharmacyGroups,
      implementationInformationGroup: {
        subtitle: 'Implementation',
        fields: getImplementationInformationFields(watch(), maps),
        columns: 2,
        handleSubmit,
        register,
        editable: true,
        formMethods,
        tab: IMPLEMENTATION_ITEM,
      },
      eligibilityGroup: {
        subtitle: 'Eligibility',
        fields: getEligibilityFields(watch(), maps),
        columns: 2,
        handleSubmit,
        register,
        editable: true,
        formMethods,
        tab: ELIGIBILITY_ITEM,
      },
      ...([
        productNames.CMK_360,
        productNames.ESI_360,
        productNames.OPT_360,
        productNames.IRX_360,
        productNames.CMK_DIRECT,
        productNames.ESI_DIRECT,
      ].includes(productName as productNames)
        ? {
            claimsCoverGroup: {
              subtitle: 'Claims Cover',
              fields: getClaimsCoverFields(watch(), maps),
              columns: 2,
              handleSubmit,
              register,
              editable: true,
              formMethods,
              tab: CLAIMS_COVER_ITEM,
            },
            fsaHraHsaGroup: {
              subtitle: 'FSA/HRA/HSA',
              fields: getFsaHraHsaFields(watch(), maps),
              columns: 2,
              handleSubmit,
              register,
              editable: true,
              formMethods,
              tab: FSA_HRA_HSA_ITEM,
            },
          }
        : {}),
    },
  };
}
