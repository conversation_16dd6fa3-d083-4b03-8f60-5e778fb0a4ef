// getProductSetup.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

/**
 * getProductSetup - Generates an array of TemplateFieldConfig for product setup fields.
 *
 * @param formData - The organization details containing product setup data.
 * @returns Array of field configurations for the product setup section.
 */
export const getProductSetup = (
  formData: OrganizationDetails,
  selectedIndex: number
): TemplateFieldConfig[] => {
  return [
    {
      label: 'Core RxB Product Name',
      type: 'text' as const,
      value: formData.plan?.product?.name,
      name: 'plan.product.name',
      placeholder: 'Select Core RxB Product Name',
      infoText: 'Please enter Core RxB Product Name',
      isRequired: true,
      validations: z.string().min(1, 'Core RxB Product Name is required'),
    },
    {
      label: 'Core RxB Product Effective Date',
      type: 'datepicker' as const,
      value: formData.plan?.effective_date,
      name: 'plan.effective_date',
      placeholder: 'Select Core RxB Product Effective Date',
      infoText: 'Choose Core RxB Product Effective Date',
      isRequired: true,
      isDisabled: true,
      validations: z
        .string()
        .min(1, 'Core RxB Product Effective Date is required'),
    },
    {
      label: 'Core RxB Product Termination Date',
      type: 'datepicker' as const,
      value: formData.plan?.expiration_date,
      name: 'plan.expiration_date',
      placeholder: 'Select Core RxB Product Termination Date',
      infoText: 'Choose Core RxB Product Termination Date',
      isRequired: true,
      isDisabled: true,
      validations: z
        .string()
        .min(1, 'Core RxB Product Termination Date is required'),
    },
  ];
};
