// getIImplementationGrandFatherFields.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { implementationGrandFatherConfig } from '../Config/implementationGrandFatherConfig';

export const getImplementationGrandFatherFields = (
  formData: OrganizationDetails,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planData = formData?.plan;
  return [
    {
      label: 'Grandfather Prior Authorization',
      value: planData?.grandfather_pa_ind,
      name: implementationGrandFatherConfig.grandfather_pa_ind,
      type: 'dropdownSelect' as const,
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Grandfather PA Timeframe',
      value: planData?.grandfather_pa_timeframe_ind,
      name: implementationGrandFatherConfig.grandfather_pa_timeframe_ind,
      type: 'dropdownSelect' as const,
      optionsMap: maps.grandfatherPaTimeframeMap,
    },
    {
      label: 'Grandfather PAs bypass High Dollar Claim Review',
      value: planData?.grandfather_pa_bypass_hdcr_ind,
      name: implementationGrandFatherConfig.grandfather_pa_bypass_hdcr_ind,
      type: 'dropdownSelect' as const,
      optionsMap: maps.yesNoMap,
    },
    {
      label: 'Grandfather PA Note',
      value: planData?.grandfather_pa_note,
      name: implementationGrandFatherConfig.grandfather_pa_note,
      type: 'textarea' as const,
      placeholder: 'Enter your notes',
      infoText: 'Please enter Grandfather PA Notes',
      validations: z.string().max(2000, 'Value cannot exceed 2000 characters'),
      rows: 5,
      customProps: {
        minHeight: '120px',
        overflow: 'hidden',
      },
    },
    {
      label: 'Other Grandfathering',
      value: planData?.grandfather_other_note,
      name: implementationGrandFatherConfig.grandfather_other_note,
      type: 'textarea' as const,
      placeholder: 'Enter your notes',
      infoText: 'Please enter Other Grandfathering Notes',
      validations: z.string().max(2000, 'Value cannot exceed 2000 characters'),
      rows: 5,
      customProps: {
        minHeight: '120px',
        overflow: 'hidden',
      },
    },
  ];
};
