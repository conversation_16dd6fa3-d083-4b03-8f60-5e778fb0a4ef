import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { formatDate } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import z from 'zod';

import { implementationConfig } from '../Config/implementationConfig';
export const getImplementationInformationFields = (
  orgDetails: Partial<OrganizationDetails>,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => [
  {
    label: 'Implementation Start Date',
    name: implementationConfig.implementation_start_date,
    value: formatDate(orgDetails?.plan?.implementation_start_date),
    type: 'datepicker',
    placeholder: 'Select Implementation Start Date',
    validations: z.date(),
    isRequired: true,
  },
  {
    label: 'Implementation Timeline',
    name: implementationConfig.implementation_timeline,
    value: orgDetails?.plan?.implementation_timeline,
    type: 'dropdownSelect',
    optionsMap: maps.implementationTimelineMap,
    isRequired: true,
    placeholder: 'Select an Option',
  },
  {
    label: 'Open Enrollment Start',
    name: implementationConfig.open_enrollment_start_date,
    value: formatDate(orgDetails?.plan?.open_enrollment_start_date),
    type: 'datepicker',
    infoText:
      'The earliest date members can enroll in / change benefit elections',
    placeholder: 'Select Open Enrollment Start Date',
    validations: z.date(),
  },
  {
    label: 'Open Enrollment End',
    name: implementationConfig.open_enrollment_end_date,
    value: formatDate(orgDetails?.plan?.open_enrollment_end_date),
    type: 'datepicker',
    infoText:
      'The latest date members can enroll in / change benefit elections',
    placeholder: 'Select Open Enrollment End Date',
    validations: z.date(),
  },
  {
    label: 'Open Enrollment Support',
    name: implementationConfig.open_enrollment_support,
    value: orgDetails?.plan?.open_enrollment_support,
    type: 'input',
    infoText: 'How does client want RxB to support OE? Flyers, meetings, etc.',
    placeholder: 'Enter Open Enrollment Support',
    validations: z
      .string()
      .max(255, 'Open Enrollment Support cannot exceed 255 characters'),
  },
];
