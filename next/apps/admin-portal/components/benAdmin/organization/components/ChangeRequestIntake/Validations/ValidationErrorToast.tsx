import { CloseIcon } from '@chakra-ui/icons';
import { Box, Button, Flex, Icon, IconButton, Text } from '@chakra-ui/react';
import React from 'react';
import { PiWarningOctagonFill } from 'react-icons/pi';

export interface NavigationLink {
  name: string;
  navigationConstant: string;
  mode: string;
}

interface ValidationErrorToastProps {
  errorMessage: string;
  navigationLinks: NavigationLink[];
  onNavigate: (mode: string, navigationConstant: string) => void;
  onClose: () => void;
}

export const ValidationErrorToast: React.FC<ValidationErrorToastProps> = ({
  errorMessage,
  navigationLinks,
  onNavigate,
  onClose,
}) => (
  <Box
    bg="#FEF0EF"
    borderLeft="4px solid #DF5D53"
    borderRadius="8px"
    p={4}
    boxShadow="md"
    maxWidth="400px"
    position="relative"
  >
    <Flex align="flex-start" mb={2}>
      <Icon
        as={PiWarningOctagonFill}
        color="#DF5D53"
        boxSize={6}
        mr={3}
        mt={1}
      />
      <Box flex="1">
        <Text fontWeight="semibold" color="#873832" fontSize="md" mb={2}>
          {errorMessage}
        </Text>
      </Box>
      <IconButton
        icon={<CloseIcon />}
        size="sm"
        variant="ghost"
        color="#873832"
        onClick={onClose}
        aria-label="Close toast"
        _hover={{ bg: 'rgba(135, 56, 50, 0.1)' }}
        ml={2}
      />
    </Flex>
    <Box ml={9}>
      <Text mb={2} color="#873832" fontSize="sm">
        Please fix the following validation errors:
      </Text>
      <Box maxH="200px" overflowY="auto">
        {navigationLinks.map((link, index) => (
          <Box key={index} mb={1}>
            <Button
              variant="link"
              color="#DF5D53"
              size="sm"
              onClick={() => onNavigate(link.mode, link.navigationConstant)}
              textDecoration="underline"
              _hover={{ color: '#B91C1C' }}
              p={0}
              h="auto"
              minH="auto"
            >
              {link.name}
            </Button>
          </Box>
        ))}
      </Box>
    </Box>
  </Box>
);
