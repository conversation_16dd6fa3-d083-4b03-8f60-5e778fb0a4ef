import { Box, Flex } from '@chakra-ui/react';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import IntakeSidebar from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/IntakeComponents/IntakeSidebar';
import { useValidationContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import {
  useSyncValidationStateWithApi,
  useValidationPageState,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationPageState';
import { useOrganizationHandlers } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHandlers';
import { useNavigation } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/useSidebarNavigation';
import {
  SidebarConfig,
  SidebarItem,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { usePathname } from 'next/navigation';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import { useIsESIProduct } from '../../../hooks/useIsESIHook';
import { getNavigationConfig } from '../Navigation/navigationHandler';
import MainContent from './MainContent';

// This component contains all the logic that depends on initialChangeRequest
export const ChangeRequestIntakeContent: React.FC<{
  initialChangeRequest: any;
}> = ({ initialChangeRequest }) => {
  const organizationDetails = useMemo(
    () =>
      (initialChangeRequest?.change_content ??
        initialChangeRequest) as OrganizationDetails,
    [initialChangeRequest]
  );
  const { formMethods } = useOrganizationHandlers({ organizationDetails });

  const isESI = useIsESIProduct(formMethods);

  // Create a dedicated auto-save handler
  const autoSaveHandler = useSaveChangeRequestHandler(formMethods, false, true);

  // Initialize and manage validations state for all UIContextScreen pages
  useValidationPageState(formMethods);

  // Get validationData from ValidationContext
  const { validationData } = useValidationContext();
  // Sync validations.success with latest validationData
  useSyncValidationStateWithApi(formMethods, validationData, autoSaveHandler);

  const pathname = usePathname() ?? '';
  const baseUrl = pathname;

  /** Below is the feature for auto-saving. This will automatically save when form is dirty
   *  it waits 3 seconds and then saves the form data.
   */

  // State for tracking auto-save
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Handle auto-saving with debounce
  const autoSaveDebounceRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  );

  // Set up a form change watcher for auto-save
  useEffect(() => {
    // We need to watch for form changes
    const subscription = formMethods.watch(() => {
      // Only proceed if the form is dirty and not currently saving
      if (formMethods.formState.isDirty && !isAutoSaving) {
        // Clear any existing timeout to implement debouncing
        if (autoSaveDebounceRef.current) {
          clearTimeout(autoSaveDebounceRef.current);
        }

        // Set a new timeout for auto-save
        autoSaveDebounceRef.current = setTimeout(() => {
          console.log('Auto-saving form data...');
          setIsAutoSaving(true);

          const currentValues = formMethods.getValues();
          autoSaveHandler(currentValues).finally(() => {
            setIsAutoSaving(false);
          });
        }, 3000);
      }
    });

    // Cleanup on unmount
    return () => {
      subscription.unsubscribe();
      if (autoSaveDebounceRef.current) {
        clearTimeout(autoSaveDebounceRef.current);
      }
    };
  }, [formMethods, autoSaveHandler, isAutoSaving]);

  // If you need to navigate to a new sidebar config, define it in getNavigationConfig
  const configs = getNavigationConfig(
    baseUrl,
    organizationDetails,
    formMethods,
    isESI
  );

  const {
    currentMode,
    activeItem,
    currentComponent,
    sidebarConfig,
    handleSelectItem: originalHandleSelectItem,
    handleBreadcrumbNavigation: originalHandleBreadcrumbNavigation,
  } = useNavigation(configs);

  const wrappedHandleSelectItem = async (item: SidebarItem) => {
    if (!isAutoSaving) {
      setIsAutoSaving(true);
      const currentValues = formMethods.getValues();
      try {
        await autoSaveHandler(currentValues);
      } catch (error) {
        console.error('Auto-save on navigation failed:', error);
      } finally {
        setIsAutoSaving(false);
      }
    }
    originalHandleSelectItem(item);
  };

  const wrappedHandleBreadcrumbNavigation = async (id: string) => {
    if (!isAutoSaving) {
      setIsAutoSaving(true);
      const currentValues = formMethods.getValues();
      try {
        await autoSaveHandler(currentValues);
      } catch (error) {
        console.error('Auto-save on navigation failed:', error);
      } finally {
        setIsAutoSaving(false);
      }
    }
    originalHandleBreadcrumbNavigation(id);
  };

  return (
    <Box w="100%" h="78.4vh" overflow="hidden">
      <Flex h="100%">
        <IntakeSidebar
          config={sidebarConfig}
          activeItemId={activeItem}
          onSelectItem={wrappedHandleSelectItem}
          formMethods={formMethods}
        />
        <MainContent
          formMethods={formMethods}
          currentComponent={currentComponent}
          currentMode={currentMode}
          sidebarConfig={sidebarConfig as SidebarConfig}
          activeItem={activeItem}
          onBreadcrumbNavigate={wrappedHandleBreadcrumbNavigation}
        />
      </Flex>
    </Box>
  );
};
