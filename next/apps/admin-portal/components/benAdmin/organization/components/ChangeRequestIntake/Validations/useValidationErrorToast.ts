import { useToast } from '@chakra-ui/react';
import React from 'react';

import type { NavigationLink } from './ValidationErrorToast';
import { ValidationErrorToast } from './ValidationErrorToast';

interface UseValidationErrorToastProps {
  onUpdateActiveItem?: (mode: string, navigationConstant: string) => void;
}

export const useValidationErrorToast = ({
  onUpdateActiveItem,
}: UseValidationErrorToastProps = {}) => {
  const toast = useToast();

  return (errorMessage: string, navigationLinks: NavigationLink[]) => {
    if (navigationLinks.length > 0) {
      toast({
        render: ({ onClose }) =>
          React.createElement(ValidationErrorToast, {
            errorMessage,
            navigationLinks,
            onNavigate: (mode: string, navigationConstant: string) => {
              onUpdateActiveItem?.(mode, navigationConstant);
            },
            onClose,
          }),
        duration: 300000, // 5 minutes
        isClosable: true,
        position: 'top-right',
      });
    }
  };
};
