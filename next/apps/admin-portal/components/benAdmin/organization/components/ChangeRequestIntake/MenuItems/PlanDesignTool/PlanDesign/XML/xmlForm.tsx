import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { getXmlPaths } from '../../../../../Tabs/PlanDesign/PlanDesign/Config/xmlConfig';
import { phoneValidation, smallintValidation } from '../../validations';

export function useXMLForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const {
    spendingAccountTypeMap,
    xmlAccumsCompleteMap,
    hraMembersAccessMap,
    esiClaimSubmissionTypeMap,
    esiInsulinMethodMap,
  } = usePicklistMaps();

  // Use existing config with the determined index
  const xmlConfig = getXmlPaths(indexToUse);
  const esi =
    currentDetails.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0]
      ?.plan_design_detail_esi?.[0];

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Shared Vendor ID',
          'input',
          xmlConfig.shared_vendor_id,
          esi?.shared_vendor_id,
          {
            infoText: 'Enter Shared Vendor ID',
            placeholder: 'Shared Vendor ID',
            validations: z
              .string()
              .max(255, 'Shared Vendor ID must be less than 255 characters'),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Vendor Policy Number',
          'input',
          xmlConfig.vendor_policy_number,
          esi?.vendor_policy_number,
          {
            infoText: 'Enter Vendor Policy Number',
            placeholder: 'Vendor Policy Number',
            validations: z
              .string()
              .max(
                255,
                'Vendor Policy Number must be less than 255 characters'
              ),
          }
        ),
        defineFormField(
          'Spending Account Type',
          'dropdownSelect',
          xmlConfig.spending_account_type_ind,
          esi?.spending_account_type_ind,
          {
            infoText: 'Choose an option for Spending Account Type',
            optionsMap: spendingAccountTypeMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'XML Accums Complete',
          'dropdownSelect',
          xmlConfig.xml_accums_complete_ind,
          esi?.xml_accums_complete_ind,
          {
            infoText: 'Choose an option for XML Accums Complete',
            optionsMap: xmlAccumsCompleteMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'HRA Members Access',
          'dropdownSelect',
          xmlConfig.hra_members_access_ind,
          esi?.hra_members_access_ind,
          {
            infoText: 'Choose an option for HRA Member Access',
            optionsMap: hraMembersAccessMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'HSA Admin Phone Number',
          'input',
          xmlConfig.hsa_admin_phone,
          esi?.hsa_admin_phone,
          {
            infoText: 'Enter HSA Admin Phone Number',
            placeholder: 'HSA Admin Phone Number',
            validations: phoneValidation,
          }
        ),
        defineFormField(
          'HSA Medical Phone Number',
          'input',
          xmlConfig.hsa_medical_phone,
          esi?.hsa_medical_phone,
          {
            infoText: 'Enter HSA Medical Phone Number',
            placeholder: 'HSA Medical Phone Number',
            validations: phoneValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Claims Submission Type',
          'dropdownSelect',
          xmlConfig.claim_submission_type_ind,
          esi?.claim_submission_type_ind,
          {
            infoText: 'Choose an option for Claims Submission Type',
            optionsMap: esiClaimSubmissionTypeMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Insulin Method Retail and Mail',
          'dropdownSelect',
          xmlConfig.insulin_method_ind,
          esi?.insulin_method_ind,
          {
            infoText: 'Choose an option for Insulin Method Retail and Mail',
            optionsMap: esiInsulinMethodMap,
            validations: z.string().optional(),
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
