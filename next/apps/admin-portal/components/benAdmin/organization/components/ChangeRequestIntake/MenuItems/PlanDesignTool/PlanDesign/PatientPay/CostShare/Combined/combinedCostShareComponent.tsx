import {
  COMPOUND_ITEM,
  XML_ITEM,
} from 'apps/admin-portal/components/benAdmin';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import CostShareComponent from '../costShareComponent';

interface CombinedCostShareComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  backIndicator?: string;
  continueIndicator?: string;
  title?: string;
  description?: string;
  modalTitle?: string;
  basePath?: string;
  sourceLabel?: string;
}

/**
 * CombinedCostShareComponent - Uses CostShareComponent to show both Standard and Unbreakable cost share tiers in one table
 */
const CombinedCostShareComponent: React.FC<CombinedCostShareComponentProps> = (props) => {
  return (
    <CostShareComponent
      {...props}
      title={props.title || 'Cost Share Tiers'}
      description={
        props.description ||
        'Select the cost share tiers that apply to this plan.'
      }
      modalTitle={props.modalTitle || 'Select Cost Share Tiers'}
      prePackagedIndFilter={undefined} // Show all tiers regardless of pre_packaged_ind
      continueIndicator={COMPOUND_ITEM}
      backIndicator={XML_ITEM}
    />
  );
};

export default CombinedCostShareComponent;
