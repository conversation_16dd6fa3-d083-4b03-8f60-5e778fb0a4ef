import { filterBy<PERSON>lanDesignField } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/filterUtils';
import React, { createContext, ReactNode, useContext, useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  ValidateByPageReturn,
  ValidationResponse,
} from '../../../../Models/interfaces';
import {
  ACCUMULATORS_GENERAL_ITEM,
  ADD_ON_PRODUCTS_ITEM,
  CLAIMS_COVER_ITEM,
  CLIENT_INFORMATION_ITEM,
  CLINICAL_DESIGN_MODE,
  COMPOUND_ITEM,
  DEDUCTIBLE_ITEM,
  DIS<PERSON>ENSE_ITEM,
  ELIGIBILITY_ITEM,
  FSA_HRA_HSA_ITEM,
  GENERAL_ITEM,
  getSectionNavigationConstants,
  ID_CARDS_ITEM,
  IMPLEMENTATION_ITEM,
  IN_HOUSE_PHARMACY_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
  MEMBER_SERVICES_ITEM,
  MEMBER_SERVICES_REVIEW_ITEM,
  OTHER_CAP_ITEM,
  PATIENT_PAY_ITEM,
  PBM_PRODUCTS_ITEM,
  PHARMACY_ACCUMULATORS_ITEM,
  PHARMACY_BENEFITS_MANAGER_ITEM,
  PHARMACY_NETWORK_ITEM,
  PLAN_DESIGN_MODE,
  PLAN_DESIGN_REVIEW_ITEM,
  PRODUCT_SET_UP_ITEM,
  PRODUCTS_AND_SERVICES_MODE,
  PRODUCTS_AND_SERVICES_REVIEW_ITEM,
  RXB_PRODUCTS_ITEM,
  STANDARD_ITEM,
  THIRD_PARTY_PRODUCTS_ITEM,
  TRANSITION_FILES_AND_DETAILS_ITEM,
  UNBREAKABLE_ITEM,
  WELCOME_KIT_AND_LETTERS_ITEM,
  XML_ITEM,
} from '../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../Navigation/uiContextEnum';
import { SIMPLE_PAGES } from '../Navigation/uiContextEnum';

interface ValidationContextType {
  validationData: ValidationResponse | null;
  isLoading: boolean;
  isError: boolean;
  refetch: () => Promise<any>;
}

export const ValidationContext = createContext<
  ValidationContextType | undefined
>(undefined);

interface ValidationProviderProps {
  children: ReactNode;
  validationData: ValidationResponse | null;
  isLoading: boolean;
  isError: boolean;
  refetch: () => Promise<any>;
}

export const ValidationProvider: React.FC<ValidationProviderProps> = ({
  children,
  validationData,
  isLoading,
  isError,
  refetch,
}) => {
  const contextValue: ValidationContextType = {
    validationData,
    isLoading,
    isError,
    refetch,
  };

  return (
    <ValidationContext.Provider value={contextValue}>
      {children}
    </ValidationContext.Provider>
  );
};

export const useValidationContext = (
  section?: string
): ValidationContextType & { errorCount?: number } => {
  const context = useContext(ValidationContext);
  if (context === undefined) {
    throw new Error(
      'useValidationContext must be used within a ValidationProvider'
    );
  }

  // Calculate error count for the section
  const errorCount = useMemo(() => {
    if (!section || !context.validationData) return 0;

    const sectionNavigationConstants = getSectionNavigationConstants(section);

    // Get the plan design index from URL if applicable
    const urlParams = new URLSearchParams(window.location.search);
    const planDesignIndex = urlParams.get('index')
      ? parseInt(urlParams.get('index')!, 10)
      : null;

    // For sections that require plan design filtering, return 0 if no index is present
    if (
      (section === PLAN_DESIGN_MODE ||
        section === PRODUCTS_AND_SERVICES_MODE ||
        section === CLINICAL_DESIGN_MODE) &&
      planDesignIndex === null
    ) {
      return 0;
    }

    let totalErrors = 0;

    // Check each navigation constant in the section
    sectionNavigationConstants.forEach((navigationConstant) => {
      const uiContextInd =
        getUIContextFromNavigationConstant(navigationConstant);
      if (!uiContextInd) return;

      const pageData = context.validationData?.results[uiContextInd.toString()];
      if (!pageData) return;

      // Filter errors for plan design pages
      const pageNum = Number(uiContextInd);
      const isPlanDesignPage = pageNum > 11;

      let errors = pageData.errors || [];
      let validationResultsErrors = pageData.validation_results?.errors || [];

      // Apply plan design filter for specific sections
      if (
        isPlanDesignPage &&
        planDesignIndex !== null &&
        (section === PLAN_DESIGN_MODE ||
          section === PRODUCTS_AND_SERVICES_MODE ||
          section === CLINICAL_DESIGN_MODE)
      ) {
        errors = filterByPlanDesignField(errors);
        validationResultsErrors = filterByPlanDesignField(
          validationResultsErrors
        );
      }

      totalErrors += errors.length + validationResultsErrors.length;
    });

    return totalErrors;
  }, [context.validationData, section]);

  return {
    ...context,
    errorCount,
  };
};

// Hook that returns same format as useValidateByPage but filtered from context (for initial load)
export const useValidateByPageFromContext = (
  navigationConstant?: string,
  contextInd?: any,
  contextParamName?: string
): ValidateByPageReturn => {
  const { validationData, isError, isLoading, refetch } =
    useValidationContext();

  const memoizedUiContextInd = useMemo(
    () =>
      navigationConstant
        ? getUIContextFromNavigationConstant(navigationConstant)
        : null,
    [navigationConstant]
  );

  const uiContextInd = contextInd ? contextInd : memoizedUiContextInd;

  const filteredValidationData = useMemo((): ValidationResponse | null => {
    if (!validationData || !uiContextInd) return null;

    let pageData = null;
    let resultKey = null;

    if (!contextParamName) {
      // Traditional lookup by ui_context_ind
      pageData = validationData.results[uiContextInd.toString()];
      resultKey = uiContextInd.toString();
    } else {
      // Search through all results to find the one with matching contextParamName field
      for (const [key, result] of Object.entries(validationData.results)) {
        if (result[contextParamName] === uiContextInd) {
          pageData = result;
          resultKey = key;
          break;
        }
      }
    }

    if (!pageData || !resultKey) return null;

    const pageNum = Number(uiContextInd);
    const isPlanDesignPage = pageNum > 11;

    // Filter errors and warnings for the current plan design index only if it's a plan design page
    const errors = isPlanDesignPage
      ? filterByPlanDesignField(pageData.errors || [])
      : pageData.errors || [];
    const warnings = isPlanDesignPage
      ? filterByPlanDesignField(pageData.warnings || [])
      : pageData.warnings || [];
    const validationResultsErrors = isPlanDesignPage
      ? filterByPlanDesignField(pageData.validation_results?.errors || [])
      : pageData.validation_results?.errors || [];
    const validationResultsWarnings = isPlanDesignPage
      ? filterByPlanDesignField(pageData.validation_results?.warnings || [])
      : pageData.validation_results?.warnings || [];

    let status: string;

    if (errors.length > 0 || validationResultsErrors.length > 0) {
      status = 'failed';
    } else if (warnings.length > 0 || validationResultsWarnings.length > 0) {
      status = 'success';
    } else {
      status = '';
    }

    return {
      message: validationData.message,
      results: {
        [resultKey]: {
          ...pageData,
          status: status,
          errors,
          warnings,
          validation_results: {
            errors: validationResultsErrors,
            warnings: validationResultsWarnings,
          },
        },
      },
    };
  }, [validationData, uiContextInd, contextParamName]);

  // Calculate error count outside of useMemo
  const errorCount = useMemo(() => {
    if (!filteredValidationData) return 0;

    const pageData = Object.values(filteredValidationData.results)[0];
    if (!pageData) return 0;

    // Use the same filtering logic as in the filteredValidationData
    const pageNum = Number(uiContextInd);
    const isPlanDesignPage = pageNum > 11;

    const errors = isPlanDesignPage
      ? filterByPlanDesignField(pageData.errors || [])
      : pageData.errors || [];
    const validationResultsErrors = isPlanDesignPage
      ? filterByPlanDesignField(pageData.validation_results?.errors || [])
      : pageData.validation_results?.errors || [];

    return errors.length + validationResultsErrors.length;
  }, [filteredValidationData, uiContextInd]);

  return {
    validationData: filteredValidationData,
    isError,
    isLoading,
    refetch,
    errorCount,
  };
};

/**
 * Hook to check if a specific page has validation errors
 */
export const usePageHasErrors = (navigationConstant: string): boolean => {
  const { validationData } = useValidationContext();

  return useMemo(() => {
    if (!validationData) return false;

    const uiContextInd = getUIContextFromNavigationConstant(navigationConstant);
    if (!uiContextInd) return false;

    const pageData = validationData.results[uiContextInd.toString()];
    if (!pageData) return false;

    const filteredErrors = filterByPlanDesignField(pageData.errors || []);
    return filteredErrors.length > 0;
  }, [validationData, navigationConstant]);
};

/**
 * Hook to check if multiple pages have validation errors
 */
export const usePagesHaveErrors = (
  navigationConstants: string[]
): Record<string, boolean> => {
  const { validationData } = useValidationContext();

  return useMemo(() => {
    const result: Record<string, boolean> = {};

    if (!validationData) {
      navigationConstants.forEach((constant) => {
        result[constant] = false;
      });
      return result;
    }

    navigationConstants.forEach((constant) => {
      const uiContextInd = getUIContextFromNavigationConstant(constant);
      if (!uiContextInd) {
        result[constant] = false;
        return;
      }

      const pageData = validationData.results[uiContextInd.toString()];
      if (!pageData) {
        result[constant] = false;
        return;
      }

      const filteredErrors = filterByPlanDesignField(pageData.errors || []);
      result[constant] = filteredErrors.length > 0;
    });

    return result;
  }, [validationData, navigationConstants]);
};

/**
 * VALIDATION-BASED MENU ITEM DISABLING SYSTEM
 *
 * This system provides automatic menu item disabling based on validation state across
 * the entire Change Request Intake application. Here's how it works:
 *
 * 1. VALIDATION DATA FLOW:
 *    - ValidationProvider (at page level) fetches and provides validation data for all pages
 *    - useValidationContext provides access to this data anywhere in the component tree
 *    - Individual components use useValidateByPageFromContext to get page-specific validation
 *
 * 2. MENU ITEM DEPENDENCY SYSTEM:
 *    - MENU_DEPENDENCIES defines which pages must be error-free before other pages become accessible
 *    - For example: XML_ITEM requires GENERAL_ITEM to be error-free
 *    - This creates a logical flow where users must fix errors in order
 *
 * 3. DYNAMIC DISABLING:
 *    - useMenuItemStates hook calculates which menu items should be disabled
 *    - Menu items are disabled if any of their dependencies have validation errors
 *    - State updates automatically when validation data changes (e.g., after continue actions)
 *
 * 4. INTEGRATION WITH SIDEBAR CONFIGS:
 *    - useValidationAwareNavigation provides validation-aware navigation configuration
 *    - Sidebar config functions accept validationDisabledStates parameter
 *    - Each menu item uses isItemDisabled(itemId) to determine its disabled state
 *
 * 5. USAGE IN COMPONENTS:
 *    - Replace getNavigationConfig with useValidationAwareNavigation in navigation components
 *    - Update sidebar config functions to accept and use validationDisabledStates parameter
 *    - Apply disabled property to individual menu items using isItemDisabled helper
 *
 * EXAMPLE IMPLEMENTATION:
 *
 * In a sidebar config:
 * ```typescript
 * export const getSomeConfig = (
 *   changeRequest: OrganizationDetails,
 *   formMethods: UseFormReturn<any>,
 *   navigateFn?: (section: string, tab: string) => void,
 *   validationDisabledStates?: Record<string, boolean>
 * ): SidebarConfig => {
 *   const isItemDisabled = (itemId: string): boolean => {
 *     return validationDisabledStates?.[itemId] ?? false;
 *   };
 *
 *   return {
 *     sections: [{
 *       title: 'Section',
 *       items: [{
 *         id: 'some-item',
 *         label: 'Some Item',
 *         disabled: isItemDisabled('some-item'),
 *         component: <SomeComponent />
 *       }]
 *     }]
 *   };
 * };
 * ```
 *
 * To add new dependency rules, simply update MENU_DEPENDENCIES below.
 *
 * Define dependency rules for menu items
 * Each key is a menu item, and the value is an array of dependencies that must be error-free
 */
export const MENU_DEPENDENCIES: Record<string, string[]> = {
  // Plan Design dependencies
  [XML_ITEM]: [GENERAL_ITEM],
  [PATIENT_PAY_ITEM]: [GENERAL_ITEM, XML_ITEM],
  [STANDARD_ITEM]: [GENERAL_ITEM, XML_ITEM],
  [UNBREAKABLE_ITEM]: [GENERAL_ITEM, XML_ITEM],
  [COMPOUND_ITEM]: [GENERAL_ITEM, XML_ITEM],
  [DISPENSE_ITEM]: [GENERAL_ITEM, XML_ITEM],
  [PHARMACY_ACCUMULATORS_ITEM]: [GENERAL_ITEM, XML_ITEM],
  [ACCUMULATORS_GENERAL_ITEM]: [GENERAL_ITEM, XML_ITEM],
  [DEDUCTIBLE_ITEM]: [GENERAL_ITEM, XML_ITEM, ACCUMULATORS_GENERAL_ITEM],
  [MAXIMUM_OUT_OF_POCKET_ITEM]: [
    GENERAL_ITEM,
    XML_ITEM,
    ACCUMULATORS_GENERAL_ITEM,
  ],
  [OTHER_CAP_ITEM]: [GENERAL_ITEM, XML_ITEM, ACCUMULATORS_GENERAL_ITEM],
  [PLAN_DESIGN_REVIEW_ITEM]: [GENERAL_ITEM, XML_ITEM],

  // Client Profile dependencies
  [PHARMACY_BENEFITS_MANAGER_ITEM]: [CLIENT_INFORMATION_ITEM],
  [IN_HOUSE_PHARMACY_ITEM]: [
    CLIENT_INFORMATION_ITEM,
    PHARMACY_BENEFITS_MANAGER_ITEM,
  ],
  [IMPLEMENTATION_ITEM]: [CLIENT_INFORMATION_ITEM],
  [ELIGIBILITY_ITEM]: [CLIENT_INFORMATION_ITEM],
  [CLAIMS_COVER_ITEM]: [CLIENT_INFORMATION_ITEM],
  [FSA_HRA_HSA_ITEM]: [CLIENT_INFORMATION_ITEM],

  // Member Experience dependencies
  [TRANSITION_FILES_AND_DETAILS_ITEM]: [ID_CARDS_ITEM],
  [WELCOME_KIT_AND_LETTERS_ITEM]: [ID_CARDS_ITEM],
  [MEMBER_SERVICES_ITEM]: [ID_CARDS_ITEM],
  [MEMBER_SERVICES_REVIEW_ITEM]: [ID_CARDS_ITEM, MEMBER_SERVICES_ITEM],

  // Products and Services dependencies
  [PHARMACY_NETWORK_ITEM]: [PRODUCT_SET_UP_ITEM],
  [RXB_PRODUCTS_ITEM]: [PRODUCT_SET_UP_ITEM],
  [PBM_PRODUCTS_ITEM]: [PRODUCT_SET_UP_ITEM],
  [THIRD_PARTY_PRODUCTS_ITEM]: [PRODUCT_SET_UP_ITEM],
  [ADD_ON_PRODUCTS_ITEM]: [PRODUCT_SET_UP_ITEM],
  [PRODUCTS_AND_SERVICES_REVIEW_ITEM]: [PRODUCT_SET_UP_ITEM],
};

/**
 * Hook to determine which menu items should be disabled based on validation state
 */
export const useMenuItemStates = (
  menuItems: string[]
): Record<string, boolean> => {
  const { validationData } = useValidationContext();

  return useMemo(() => {
    const result: Record<string, boolean> = {};

    if (!validationData) {
      // If no validation data, only disable items that have dependencies
      menuItems.forEach((item) => {
        result[item] = !!MENU_DEPENDENCIES[item];
      });
      return result;
    }

    // Function to check if a page has errors
    const pageHasErrors = (navigationConstant: string): boolean => {
      const uiContextInd =
        getUIContextFromNavigationConstant(navigationConstant);
      if (!uiContextInd) return false;

      const pageData = validationData.results[uiContextInd.toString()];
      if (!pageData) return false;

      const filteredErrors = filterByPlanDesignField(pageData.errors || []);
      return filteredErrors.length > 0;
    };

    menuItems.forEach((item) => {
      const dependencies = MENU_DEPENDENCIES[item];

      if (!dependencies || dependencies.length === 0) {
        // No dependencies, never disabled due to validation
        result[item] = false;
      } else {
        // Check if any dependency has errors
        const hasErrorsInDependencies = dependencies.some((dep) =>
          pageHasErrors(dep)
        );
        result[item] = hasErrorsInDependencies;
      }
    });

    return result;
  }, [validationData, menuItems]);
};

/**
 * Reusable hook to set initialized state for a page (and plan design index, if applicable)
 */
export function useSetPageInitialized() {
  return (
    formMethods: UseFormReturn<any>,
    page: string | number,
    planDesignIndex?: number
  ) => {
    const validations = formMethods.getValues('validations') || {};
    const pageKey = String(page);
    const pageNum = Number(page);
    let updated = false;
    if (SIMPLE_PAGES.includes(pageNum)) {
      if (validations[pageKey] && validations[pageKey].initialized !== true) {
        validations[pageKey].initialized = true;
        updated = true;
      }
    } else if (
      validations[pageKey] &&
      Array.isArray(validations[pageKey].initialized) &&
      typeof planDesignIndex === 'number' &&
      validations[pageKey].initialized[planDesignIndex] !== true
    ) {
      validations[pageKey].initialized[planDesignIndex] = true;
      updated = true;
    }
    if (updated) {
      formMethods.setValue(
        'validations',
        { ...validations },
        {
          shouldValidate: false,
          shouldDirty: false,
        }
      );
    }
  };
}
