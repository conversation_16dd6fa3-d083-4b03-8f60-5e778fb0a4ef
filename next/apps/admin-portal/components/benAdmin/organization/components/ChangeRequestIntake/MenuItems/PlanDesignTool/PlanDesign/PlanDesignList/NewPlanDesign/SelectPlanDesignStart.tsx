import {
  Box,
  Divider,
  Flex,
  Radio,
  RadioGroup,
  Text,
  VStack,
} from '@chakra-ui/react';
import { Organization } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { picklistToOptionsMap } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistConstants';
import DropdownSelect from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/DropdownSelect';

export const SelectPlanDesignStart = ({
  onRadioChange,
  selection,
  organization,
  setOrganization,
  currentOrgPlanDesignCount,
  allOrgsByPBM,
  isOrgsLoading,
}: {
  onRadioChange: (value: string) => void;
  selection: 'blank' | 'copyFromThisOrg' | 'copyFromOtherOrg' | undefined;
  organization: string | null;
  setOrganization: (org: string | null) => void;
  currentOrgPlanDesignCount?: number;
  allOrgsByPBM: any;
  isOrgsLoading: boolean;
}) => {
  const organizationPicklist = () => {
    return picklistToOptionsMap(
      allOrgsByPBM?.map((org: Organization) => {
        return {
          label: org.name,
          value: org.organization_id,
        };
      })
    );
  };

  return (
    <VStack spacing={9} align="stretch">
      <Text fontSize="2xl" color="#022F6C">
        To help you save time creating a Plan Design(s), we have a few options
        to help you get started:
      </Text>

      <Flex direction="column" color="#69696A">
        <Text fontWeight={500}>Where do you want to start?</Text>
        <Box mt={2} p={3} borderRadius="8px" border="1px solid #EBEBEC">
          <RadioGroup
            onChange={onRadioChange}
            colorScheme="green"
            value={selection}
          >
            <VStack align="start">
              <Radio value="blank">
                <Flex direction="column">
                  <Text fontWeight={600} fontSize="lg">
                    Build Plan Design(s) from scratch.
                  </Text>
                  <Text fontSize="md">
                    Create an empty Plan Design with no information added.
                  </Text>
                </Flex>
              </Radio>
              <Divider />
              {currentOrgPlanDesignCount && (
                <>
                  <Radio value="copyFromThisOrg">
                    <Flex direction="column">
                      <Text fontWeight={600} fontSize="lg">
                        Copy Plan Design(s) created in this organization.
                      </Text>
                      <Text fontSize="md">
                        <Text fontWeight={700} color="#02a6ce" display="inline">
                          {currentOrgPlanDesignCount} Plan Design(s)
                        </Text>{' '}
                        available to copy from. Choose the Plan Design(s) in the
                        next page.
                      </Text>
                    </Flex>
                  </Radio>
                  <Divider />
                </>
              )}
              <Radio value="copyFromOtherOrg">
                <Flex direction="column">
                  <Text fontWeight={600} fontSize="lg">
                    Copy Plan Design(s) from a different organization.
                  </Text>
                  {selection === 'copyFromOtherOrg' ? (
                    <DropdownSelect
                      formMethods={undefined as any}
                      name="search-organizations"
                      onChange={(name, orgId) => {
                        setOrganization(orgId);
                      }}
                      value={organization ?? ''}
                      optionsMap={organizationPicklist()}
                      allowSearch
                      isOptionsMapLoading={isOrgsLoading}
                      sortOptionsByLabel
                    />
                  ) : (
                    <Text fontSize="md">
                      Search for the organization. You can choose the Plan
                      Design(s) in the next page.
                    </Text>
                  )}
                </Flex>
              </Radio>
            </VStack>
          </RadioGroup>
        </Box>
      </Flex>
    </VStack>
  );
};
