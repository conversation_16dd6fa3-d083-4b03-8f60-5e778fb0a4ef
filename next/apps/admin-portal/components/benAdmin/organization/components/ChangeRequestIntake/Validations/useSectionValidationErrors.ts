import { FormSections } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/types/types';
import { filterByPlanDesignField } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/filterUtils';
import { useMemo } from 'react';

import { getUIContextFromNavigationConstant } from '../Navigation/uiContextEnum';
import { useValidationContext } from './ValidationContext';

/**
 * Hook to get validation error count for a specific section
 */
export const useSectionValidationErrors = (
  formSections?: FormSections
): number => {
  const { validationData } = useValidationContext();

  return useMemo(() => {
    if (!validationData || !formSections?.id) {
      return 0;
    }

    // Get all UI context indices that might relate to this section
    const navigationConstant = formSections.id;

    const uiContextInd = getUIContextFromNavigationConstant(navigationConstant);

    if (!uiContextInd) {
      return 0;
    }

    // Look for validation results for this ui_context_ind
    const pageData = validationData.results[uiContextInd.toString()];

    if (!pageData) {
      return 0;
    }

    console.log('pageData', pageData);

    // Get errors from both sources
    const directErrors = pageData.errors || [];
    const validationResultsErrors = pageData.validation_results?.errors || [];

    // Filter errors for plan design pages if needed
    const pageNum = Number(uiContextInd);
    const isPlanDesignPage = pageNum > 11;

    const filteredDirectErrors = isPlanDesignPage
      ? filterByPlanDesignField(directErrors)
      : directErrors;

    const filteredValidationResultsErrors = isPlanDesignPage
      ? filterByPlanDesignField(validationResultsErrors)
      : validationResultsErrors;

    const totalErrors =
      filteredDirectErrors.length + filteredValidationResultsErrors.length;

    return totalErrors;
  }, [validationData, formSections?.id]);
};

/**
 * Hook to get validation error counts for multiple sections at once
 * This is more efficient when you need errors for multiple sections
 */
export const useMultipleSectionValidationErrors = (
  formSectionsList: (FormSections | undefined)[]
): Record<string, number> => {
  const { validationData } = useValidationContext();

  return useMemo(() => {
    const result: Record<string, number> = {};

    if (!validationData) {
      return result;
    }

    formSectionsList.forEach((formSections) => {
      if (!formSections?.id) return;

      const navigationConstant = formSections.id;
      const uiContextInd =
        getUIContextFromNavigationConstant(navigationConstant);

      if (!uiContextInd) {
        result[navigationConstant] = 0;
        return;
      }

      const pageData = validationData.results[uiContextInd.toString()];
      if (!pageData) {
        result[navigationConstant] = 0;
        return;
      }
      console.log('pageData', pageData);

      const directErrors = pageData.errors || [];
      const validationResultsErrors = pageData.validation_results?.errors || [];

      const pageNum = Number(uiContextInd);
      const isPlanDesignPage = pageNum > 11;

      const filteredDirectErrors = isPlanDesignPage
        ? filterByPlanDesignField(directErrors)
        : directErrors;

      const filteredValidationResultsErrors = isPlanDesignPage
        ? filterByPlanDesignField(validationResultsErrors)
        : validationResultsErrors;

      result[navigationConstant] =
        filteredDirectErrors.length + filteredValidationResultsErrors.length;
    });

    return result;
  }, [validationData, formSectionsList]);
};
