import { Box, Spinner, Text, VStack } from '@chakra-ui/react';
import {
  getGeneralPaths,
  STANDARD_ITEM,
  UNBREAKABLE_ITEM,
} from 'apps/admin-portal/components/benAdmin';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useIsESIProduct } from 'apps/admin-portal/components/benAdmin/organization/hooks/useIsESIHook';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { InlineEditTable } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/FormTable';
import { bopTablestyles } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Styles/styles';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { UseFormReturn, useWatch } from 'react-hook-form';

import { useValidateByPageFromContext } from '../../../../../Validations/ValidationContext';
import CostShareEditModal from './CostShareEditModal';
import { useCostShareColumns } from './useCostShareColumns';
import { useCostShareHandlers } from './useCostShareHandlers';
import {
  filterTiersByPrePackaged,
  getFieldPath,
  getFieldValidationState,
  getIndexFromURL,
  getRowValidationState,
  parseValidationErrors,
  PRE_PACKAGED_IND,
  transformTierForDisplay,
} from './utils/costShareUtils';

interface CostShareComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  backIndicator: string;
  continueIndicator: string;
  isUnbreakable?: boolean;
  title?: string;
  description?: string;
  modalTitle?: string;
  basePath?: string;
  sourceLabel?: string;
  prePackagedIndFilter?: number;
}

const CostShareComponent: React.FC<CostShareComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
  backIndicator,
  continueIndicator,
  title = 'Cost Share Design',
  description = 'Select the cost share tiers that apply to this plan.',
  prePackagedIndFilter = PRE_PACKAGED_IND.STANDARD,
}) => {
  const planDesignIndex = useMemo(() => getIndexFromURL(), []);
  const fieldPath = useMemo(
    () => getFieldPath(planDesignIndex),
    [planDesignIndex]
  );

  const pageConstant =
    prePackagedIndFilter === undefined
      ? STANDARD_ITEM // Use STANDARD_ITEM for combined view
      : prePackagedIndFilter === PRE_PACKAGED_IND.STANDARD
      ? STANDARD_ITEM
      : UNBREAKABLE_ITEM;

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(pageConstant);

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(pageConstant, continueIndicator);
  const handleModalContinue = createContinueHandler(pageConstant, continueIndicator);

  // Parse validation errors into a map for table highlighting
  const validationMap = useMemo(() => {
    return parseValidationErrors(
      validationData,
      planDesignIndex,
      prePackagedIndFilter,
      formMethods
    );
  }, [validationData, planDesignIndex, prePackagedIndFilter, formMethods]);

  // Watch cost share tiers for real-time updates
  const watchedTiers = useWatch({
    control: formMethods.control,
    name: fieldPath,
    defaultValue: [],
  });

  const processedTableData = useMemo(() => {
    const transformedTiers = (watchedTiers || []).map(transformTierForDisplay);
    // For combined view, show all tiers regardless of pre_packaged_ind
    // For separate views, filter by prePackagedIndFilter
    const filtered = prePackagedIndFilter === undefined
      ? transformedTiers
      : filterTiersByPrePackaged(transformedTiers, prePackagedIndFilter);
    return filtered;
  }, [watchedTiers, prePackagedIndFilter]);

  // Create validation callback functions for the table
  const getRowValidationStateCallback = useCallback(
    (rowIndex: number) => {
      // Get the original index from the row data
      const originalIndex = processedTableData[rowIndex]?._index;
      if (originalIndex === undefined)
        return { hasErrors: false, hasWarnings: false };
      return getRowValidationState(validationMap, originalIndex);
    },
    [validationMap, processedTableData]
  );

  const getFieldValidationStateCallback = useCallback(
    (rowIndex: number, fieldKey: string) => {
      // Get the original index from the row data
      const originalIndex = processedTableData[rowIndex]?._index;
      if (originalIndex === undefined)
        return { hasError: false, hasWarning: false };
      return getFieldValidationState(validationMap, originalIndex, fieldKey);
    },
    [validationMap, processedTableData]
  );

  const picklistMaps = usePicklistMaps();
  const isESIProduct = useIsESIProduct(formMethods);

  const canGeneratePrepackageTiers =
    isESIProduct &&
    formMethods.watch(
      getGeneralPaths(planDesignIndex).pre_packaged_copays_ind
    ) !== '1';

  // Memoized columns using the new hook
  const showTierType = prePackagedIndFilter === undefined;
  const columns = useCostShareColumns(picklistMaps, isESIProduct, showTierType);

  // Use the extracted handlers hook
  const {
    isModalOpen,
    selectedTierForModal,
    handleAddNew,
    handleDeleteRow,
    handleDuplicateRow,
    handleGeneratePrepackageTiers,
    handleEditInModal,
    handleModalSave,
    handleModalClose,
    handleBack,
    handleCellUpdate,
    isAdding,
    isDuplicating,
  } = useCostShareHandlers({
    formMethods,
    fieldPath,
    prePackagedIndFilter,
    tableData: processedTableData,
    onUpdateActiveItem,
    backIndicator,
    continueIndicator,
  });

  // Group highlight state
  const [hoveredSignature, setHoveredSignature] = useState<string | null>(null);

  // Flash highlight state for newly duplicated rows
  const [flashingRows, setFlashingRows] = useState<Set<number>>(new Set());

  // Track previous data length to detect deletions
  const prevDataLengthRef = useRef(processedTableData.length);

  // Track if we're currently adding a new row to prevent flashing
  const wasAddingRef = useRef(false);

  // Effect to handle flashing for justDuplicated rows and clear on deletion
  useEffect(() => {
    // Track adding state
    if (isAdding) {
      wasAddingRef.current = true;
    }

    // Detect deletion: if current length is less than previous length
    if (processedTableData.length < prevDataLengthRef.current) {
      // Clear all flashing rows on deletion
      setFlashingRows(new Set());
      prevDataLengthRef.current = processedTableData.length;
      wasAddingRef.current = false;
      return;
    }

    // If we were adding and now we're not, skip the flash logic
    if (wasAddingRef.current && !isAdding) {
      wasAddingRef.current = false;
      prevDataLengthRef.current = processedTableData.length;
      return;
    }

    prevDataLengthRef.current = processedTableData.length;

    // Only check for duplicated rows if we're not adding and weren't just adding
    if (!isAdding && !wasAddingRef.current) {
      // Find the highest index with justDuplicated === true
      let lastDupIdx: number | null = null;
      for (let i = processedTableData.length - 1; i >= 0; i--) {
        if (processedTableData[i]?.justDuplicated) {
          lastDupIdx = i;
          break;
        }
      }

      if (lastDupIdx !== null) {
        setFlashingRows((prev) => {
          const next = new Set(prev);
          next.add(lastDupIdx!);
          return next;
        });

        // Remove the highlight after 900ms and clear justDuplicated
        const timeout = setTimeout(() => {
          setFlashingRows((prev) => {
            const next = new Set(prev);
            next.delete(lastDupIdx!);
            return next;
          });
          // Remove justDuplicated property from the row
          if (
            processedTableData[lastDupIdx!] &&
            processedTableData[lastDupIdx!].justDuplicated
          ) {
            processedTableData[lastDupIdx!].justDuplicated = false;
          }
        }, 900);

        return () => clearTimeout(timeout);
      }
    }
  }, [processedTableData, isAdding]);

  // Memoized table component
  const tableComponent = useMemo(
    () => (
      <VStack spacing={4} align="stretch" position="relative">
        <Text fontSize="lg" fontWeight="semibold">
          {prePackagedIndFilter === undefined
            ? 'Cost Share Tiers'
            : prePackagedIndFilter === PRE_PACKAGED_IND.UNBREAKABLE
            ? 'Unbreakable Cost Share Tiers'
            : 'Standard Cost Share Tiers'}{' '}
          ({processedTableData.length} tiers)
        </Text>

        <Box position="relative" maxWidth="100%" overflowX="auto">
          <InlineEditTable
            data={processedTableData}
            columns={columns}
            onCellUpdate={handleCellUpdate}
            onAddNew={handleAddNew}
            onDeleteRow={handleDeleteRow}
            onDuplicateRow={handleDuplicateRow}
            {...(canGeneratePrepackageTiers && {
              customRowActions: [
                {
                  label: 'Generate Prepackage Tiers',
                  onClick: (_row, rowIndex) =>
                    handleGeneratePrepackageTiers(rowIndex),
                },
              ],
            })}
            onEditInModal={handleEditInModal}
            addNewButtonText={
              isAdding ? 'Adding...' : 'Add New Cost Share Tier'
            }
            showAddNew={true}
            showRowActions={true}
            rowKey={(row) => `${row._index}-${row.name}`}
            tableStyles={{
              ...bopTablestyles.table,
              fontSize: 'xs',
              '& th': bopTablestyles.th(true), // Enable compact mode
              '& td': bopTablestyles.td(true), // Enable compact mode
            }}
            getRowValidationState={getRowValidationStateCallback}
            getFieldValidationState={getFieldValidationStateCallback}
            // Row highlight logic
            rowProps={(_row: any, index: number) => {
              const isFlashing = flashingRows.has(index);
              const rowValidation = getRowValidationStateCallback(index);

              // Validation errors/warnings take precedence over hover/flash states
              if (rowValidation.hasErrors || rowValidation.hasWarnings) {
                return {
                  onMouseEnter: () => setHoveredSignature(`${index}`),
                  onMouseLeave: () => setHoveredSignature(null),
                  // Let the table component handle validation styling
                };
              }

              return {
                onMouseEnter: () => setHoveredSignature(`${index}`),
                onMouseLeave: () => setHoveredSignature(null),
                style: isFlashing
                  ? {
                      background: '#FFF9DB',
                      transition: 'background 0.5s',
                    }
                  : hoveredSignature === `${index}`
                  ? { background: '#f7fafc', transition: 'background 0.2s' }
                  : {},
              };
            }}
          />
          {(isAdding || isDuplicating !== null) && (
            <Box
              position="absolute"
              top={0}
              left={0}
              width="100%"
              height="100%"
              bg="rgba(255,255,255,0.6)"
              zIndex={10}
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <Spinner
                size="xl"
                color="gray.500"
                thickness="4px"
                speed="0.65s"
              />
            </Box>
          )}
        </Box>
      </VStack>
    ),
    [
      prePackagedIndFilter,
      processedTableData,
      columns,
      handleCellUpdate,
      handleAddNew,
      handleDeleteRow,
      handleDuplicateRow,
      handleEditInModal,
      isAdding,
      isDuplicating,
      hoveredSignature,
      flashingRows,
      getRowValidationStateCallback,
      getFieldValidationStateCallback,
      canGeneratePrepackageTiers,
      handleGeneratePrepackageTiers,
    ]
  );

  const formTitle = useMemo(() => `${title}`, [title]);

  return (
    <>
      <Box width="100%" position="relative">
        <GenericForm
          formName={formTitle}
          formDescription={<span>{description}</span>}
          subCategories={[]}
          formMethods={formMethods}
          table={tableComponent}
          onBack={handleBack}
          onContinue={handleContinue}
          showButtons={true}
          continueButtonText="Continue"
          isProcessing={isLoading}
        />
      </Box>

      <CostShareEditModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        tier={selectedTierForModal}
        onSave={handleModalSave}
        prePackagedIndFilter={prePackagedIndFilter}
        isESI={isESIProduct}
        validationMap={validationMap}
        refetch={handleModalContinue}
      />
    </>
  );
};

export default React.memo(CostShareComponent);
