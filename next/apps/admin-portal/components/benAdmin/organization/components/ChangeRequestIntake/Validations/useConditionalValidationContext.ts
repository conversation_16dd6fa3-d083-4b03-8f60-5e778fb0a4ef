import { useContext, useEffect, useMemo, useState } from 'react';

import { filterByPlanDesignField } from '../../../../ReusableComponents/Components/SideNavBar/filterUtils';
import {
  CLINICAL_DESIGN_MODE,
  getSectionNavigationConstants,
  PLAN_DESIGN_MODE,
  PRODUCTS_AND_SERVICES_MODE,
} from '../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../Navigation/uiContextEnum';
import { ValidationContext } from './ValidationContext';

interface ValidationContextType {
  validationData: any;
  isLoading: boolean;
  isError: boolean;
  refetch: () => Promise<any>;
  errorCount?: number;
  tabErrors?: string[];
}

// Custom hook to track URL parameters
const useUrlParams = () => {
  const [urlParams, setUrlParams] = useState(
    () => new URLSearchParams(window.location.search)
  );

  useEffect(() => {
    // Function to update URL params
    const updateUrlParams = () => {
      setUrlParams(new URLSearchParams(window.location.search));
    };

    // Listen for popstate (back/forward navigation)
    window.addEventListener('popstate', updateUrlParams);

    // For Next.js apps, we can also listen to route changes if available
    if (typeof window !== 'undefined') {
      // Check URL periodically as a fallback
      const checkUrl = () => {
        const newParams = new URLSearchParams(window.location.search);
        const currentParams = urlParams.toString();
        const newParamsString = newParams.toString();

        if (currentParams !== newParamsString) {
          setUrlParams(newParams);
        }
      };

      // Use a more efficient approach with periodic checks
      const intervalId = setInterval(checkUrl, 100);

      return () => {
        window.removeEventListener('popstate', updateUrlParams);
        clearInterval(intervalId);
      };
    }

    return () => {
      window.removeEventListener('popstate', updateUrlParams);
    };
  }, [urlParams]);

  return urlParams;
};

export const useConditionalValidationContext = (
  section?: string
): ValidationContextType => {
  const context = useContext(ValidationContext);
  const urlParams = useUrlParams();

  // Always call useMemo first, before any conditional returns
  const { errorCount, tabErrors } = useMemo(() => {
    // If ValidationProvider is not available, return 0
    if (context === undefined) {
      return { errorCount: 0, tabErrors: [] };
    }

    // If no section or validation data, return 0
    if (!section || !context.validationData)
      return { errorCount: 0, tabErrors: [] };

    const sectionNavigationConstants = getSectionNavigationConstants(section);

    // Get the plan design index from URL if applicable
    const planDesignIndex = urlParams.get('index')
      ? parseInt(urlParams.get('index')!, 10)
      : 0;

    // For sections that require plan design filtering, return 0 if no index is present
    if (
      (section === PLAN_DESIGN_MODE ||
        section === PRODUCTS_AND_SERVICES_MODE ||
        section === CLINICAL_DESIGN_MODE) &&
      planDesignIndex === null
    ) {
      return { errorCount: 0, tabErrors: [] };
    }

    let totalErrors = 0;
    const errorTabs: string[] = [];

    // Check each navigation constant in the section
    sectionNavigationConstants.forEach((navigationConstant) => {
      const uiContextInd =
        getUIContextFromNavigationConstant(navigationConstant);
      if (!uiContextInd) return;

      const pageData = context.validationData?.results[uiContextInd.toString()];
      if (!pageData) return;

      // Filter errors for plan design pages
      const pageNum = Number(uiContextInd);
      const isPlanDesignPage = pageNum > 11;

      let errors = pageData.errors || [];
      let validationResultsErrors = pageData.validation_results?.errors || [];

      // Apply plan design filter for specific sections
      if (
        isPlanDesignPage &&
        planDesignIndex !== null &&
        (section === PLAN_DESIGN_MODE ||
          section === PRODUCTS_AND_SERVICES_MODE ||
          section === CLINICAL_DESIGN_MODE)
      ) {
        errors = filterByPlanDesignField(errors, planDesignIndex);
        validationResultsErrors = filterByPlanDesignField(
          validationResultsErrors,
          planDesignIndex
        );
      }

      const pageErrorCount = errors.length + validationResultsErrors.length;
      totalErrors += pageErrorCount;

      // If this page has errors, add its navigation constant to the error tabs array
      if (pageErrorCount > 0) {
        errorTabs.push(navigationConstant);
      }
    });

    return { errorCount: totalErrors, tabErrors: errorTabs };
  }, [context, section, urlParams]);

  // If ValidationProvider is not available, return default values
  if (context === undefined) {
    return {
      validationData: null,
      isLoading: false,
      isError: false,
      refetch: () => Promise.resolve(),
      errorCount,
      tabErrors,
    };
  }

  return {
    ...context,
    errorCount,
    tabErrors,
    refetch: context.refetch,
  };
};
