import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Mo<PERSON>Footer,
  useDisclosure,
} from '@chakra-ui/react';
import { AccumulationOther } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { ConfirmationPopup } from 'apps/admin-portal/components/benAdmin/organization/components/ConfirmationPopup';
import { getOtherCapBasePath } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/otherPAConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { FC, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { productNames } from '../../../productNameConstants';
import { currencyValidation, smallintValidation } from '../../../validations';

interface OtherCapFormProps {
  initialData?: Partial<AccumulationOther>;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  itemIndex?: number;
  isNewItem?: boolean;
  productName: string;
}

export const OtherCapForm: FC<OtherCapFormProps> = ({
  initialData = {},
  onSave,
  onCancel,
  itemIndex = -1,
  isNewItem = true,
  productName,
}) => {
  const {
    esiAccumPeriodMap,
    benefitPeriodsMap,
    benefitPeriodLengthMap,
    carryoverPhaseMap,
    cdhClassCodeMap,
    accumDrugListMap,
    drugTypeStatusMap,
    formularyStatusMap,
    networkApplicabilityMap,
    networkStatusMap,
    otherAccumTypeMap,
    pharmacyChannelMap,
    pharmacyChannelAccumMap,
    sharedIndicatorMap,
    yesNoMap,
  } = usePicklistMaps();

  const basePath = getOtherCapBasePath(isNewItem, itemIndex);

  const formMethods = useForm({
    defaultValues: { [basePath]: initialData },
    shouldUnregister: false,
  });

  const currentData = formMethods.watch(basePath) || initialData;

  // Date validation state
  const [alertMessage, setAlertMessage] = useState('');
  const [isDateValid, setIsDateValid] = useState(true);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [lastEditedField, setLastEditedField] = useState<
    'effective' | 'expiration' | null
  >(null);

  const [effectiveDate, expirationDate] = formMethods.watch([
    `${basePath}.effective_date`,
    `${basePath}.expiration_date`,
  ]);

  useEffect(() => {
    if (!effectiveDate && !expirationDate) {
      setIsDateValid(true);
      setAlertMessage('');
      return;
    }

    const effective = effectiveDate ? new Date(effectiveDate) : null;
    const expiration = expirationDate ? new Date(expirationDate) : null;

    // Detect which field changed last (basic)
    if (effective && !expiration) {
      setLastEditedField('effective');
    } else if (!effective && expiration) {
      setLastEditedField('expiration');
    }

    if (effective && expiration) {
      const isInvalid = effective > expiration;

      if (isInvalid) {
        const message =
          lastEditedField === 'expiration'
            ? 'End Date cannot be an earlier date than Effective Date. Please choose a different date.'
            : 'Effective Date cannot be a later date than End Date. Please choose a different date.';

        setAlertMessage(message);
        setIsDateValid(false);
        onOpen();
      } else {
        setIsDateValid(true);
        setAlertMessage('');
        onClose();
      }
    }
  }, [effectiveDate, expirationDate, lastEditedField, onClose, onOpen]);

  useEffect(() => {
    formMethods.setValue(basePath, initialData);
  }, [basePath, formMethods, initialData]);

  const handleFormSubmit = () => {
    if (!onSave) return;
    const submitted = formMethods.getValues(basePath);
    const finalData = isNewItem
      ? { ...initialData, ...submitted }
      : { ...currentData, ...submitted };
    onSave(finalData);
  };

  const formConfig = useMemo(() => {
    return {
      subCategories: [
        defineSubCategory('', '', [
          ...(productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360 ||
          productName === productNames.IRX_360
            ? [
                defineInlineFieldGroup([
                  defineFormField(
                    'Accums Tier Type',
                    'dropdownSelect',
                    `${basePath}.other_accum_type_ind`,
                    currentData?.other_accum_type_ind,
                    {
                      infoText: 'Accums Tier Type',
                      optionsMap: otherAccumTypeMap,
                    }
                  ),
                  defineFormField(
                    'Accums Tier Name',
                    'input',
                    `${basePath}.accums_tier_name`,
                    currentData?.accums_tier_name,
                    {
                      infoText: 'Accums Tier Name',
                      placeholder: 'Enter Accums Tier Name',
                      validations: z
                        .string()
                        .max(
                          255,
                          'Accums Tier Name must be less than 255 characters'
                        )
                        .optional()
                        .nullable(),
                    }
                  ),
                ]),
                defineInlineFieldGroup([
                  defineFormField(
                    'Accums Tier Effective Date',
                    'datepicker',
                    `${basePath}.effective_date`,
                    initialData?.effective_date,
                    {
                      infoText: 'Accum Tier Effective Date',
                      validations: z.date(),
                    }
                  ),
                  defineFormField(
                    'Accums Tier End Date',
                    'datepicker',
                    `${basePath}.expiration_date`,
                    initialData?.expiration_date,
                    {
                      infoText: 'Accum Tier End Date',
                      validations: z.date().optional().nullable(),
                    }
                  ),
                ]),
                defineInlineFieldGroup([
                  defineFormField(
                    'Accums Tier PBC Order',
                    'input',
                    `${basePath}.pbc_order`,
                    initialData?.pbc_order,
                    {
                      infoText: 'Accums Tier PBC Order',
                      placeholder: 'Enter Accums Tier PBC Order',
                      validations: z.number().int().optional().nullable(),
                    }
                  ),
                  defineFormField(
                    'Accumulation Period',
                    'dropdownSelect',
                    `${basePath}.accum_period_ind`,
                    initialData?.accum_period_ind,
                    {
                      infoText: 'Accumulation Period',
                      optionsMap: esiAccumPeriodMap,
                      validations: z.string().optional(),
                    }
                  ),
                ]),
              ]
            : []),
          defineInlineFieldGroup([
            ...(productName === productNames.CMK_360 ||
            productName === productNames.ESI_360 ||
            productName === productNames.OPT_360 ||
            productName === productNames.IRX_360
              ? [
                  defineFormField(
                    'Specify Other Cap Accumulation Period',
                    'dropdownSelect',
                    `${basePath}.specify_accum_period_ind`,
                    initialData?.specify_accum_period_ind,
                    {
                      infoText: 'Specify Other Cap Accumulation Period',
                      optionsMap: benefitPeriodsMap,
                    }
                  ),
                ]
              : []),
            ...(productName === productNames.ESI_360
              ? [
                  defineFormField(
                    'Benefit Period Length',
                    'dropdownSelect',
                    `${basePath}.benefit_period_length_ind`,
                    initialData?.benefit_period_length_ind,
                    {
                      infoText: 'Benefit Period Length',
                      optionsMap: benefitPeriodLengthMap,
                    }
                  ),
                ]
              : []),
          ]),
          defineInlineFieldGroup([
            ...(productName === productNames.ESI_360
              ? [
                  defineFormField(
                    'Benefit Period Length - Other',
                    'input',
                    `${basePath}.benefit_period_length_other`,
                    initialData?.benefit_period_length_other,
                    {
                      infoText: 'Benefit Period Length - Other',
                      placeholder: 'Benefit Period Length - Other',
                      validations: z.preprocess(
                        (val) =>
                          typeof val === 'string' && !isNaN(Number(val))
                            ? Number(val)
                            : val,
                        z
                          .number()
                          .int()
                          .nullable()
                          .optional()
                          .refine((val) => val == null || val > 0, {
                            message: 'Number must be greater than 0',
                          })
                          .refine((val) => val == null || val <= 99)
                      ),
                    }
                  ),
                ]
              : []),
            ...(productName === productNames.CMK_360 ||
            productName === productNames.ESI_360 ||
            productName === productNames.OPT_360 ||
            productName === productNames.IRX_360
              ? [
                  defineFormField(
                    'Do Priming Balances Apply?',
                    'dropdownSelect',
                    `${basePath}.priming_balances_ind`,
                    initialData?.priming_balances_ind,
                    {
                      infoText: 'Do Priming Balances Apply?',
                      optionsMap: yesNoMap,
                    }
                  ),
                ]
              : []),
          ]),
          ...(productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360 ||
          productName === productNames.IRX_360
            ? [
                defineInlineFieldGroup([
                  defineFormField(
                    'Carryover Phase',
                    'dropdownSelect',
                    `${basePath}.carryover_phase_ind`,
                    initialData?.carryover_phase_ind,
                    {
                      infoText: 'Carryover Phase',
                      optionsMap: carryoverPhaseMap,
                    }
                  ),
                  defineFormField(
                    'Describe Carryover Phase',
                    'input',
                    `${basePath}.describe_carryover_phase`,
                    initialData?.describe_carryover_phase,
                    {
                      infoText: 'Describe Carryover Phase',
                      placeholder: 'Describe Carryover Phase',
                      validations: z
                        .string()
                        .max(255, 'Value must be less than 255 characters')
                        .optional()
                        .nullable(),
                    }
                  ),
                ]),
                defineInlineFieldGroup([
                  defineFormField(
                    'Individual Cap Amount',
                    'input',
                    `${basePath}.individual_plan_amount`,
                    initialData?.individual_plan_amount,
                    {
                      infoText: 'Individual Cap Amount',
                      placeholder: 'Enter Individual Cap Amount',
                      validations: currencyValidation.optional(),
                    }
                  ),
                  defineFormField(
                    'Family Cap Amount',
                    'input',
                    `${basePath}.family_plan_amount`,
                    initialData?.family_plan_amount,
                    {
                      infoText: 'Family Cap Amount',
                      placeholder: 'Enter Family Cap Amount',
                      validations: currencyValidation.optional(),
                    }
                  ),
                ]),
                defineInlineFieldGroup([
                  defineFormField(
                    'Employee +1 Cap Amount',
                    'input',
                    `${basePath}.employee_1_dep_amount`,
                    initialData?.employee_1_dep_amount,
                    {
                      infoText: 'Employee +1 Cap Amount',
                      placeholder: 'Enter Employee +1 Cap Amount',
                      validations: currencyValidation.optional(),
                    }
                  ),
                  defineFormField(
                    'Individual within Family Cap Amount',
                    'input',
                    `${basePath}.individual_within_family_amount`,
                    initialData?.individual_within_family_amount,
                    {
                      infoText: 'Individual within Family Cap Amount',
                      placeholder: 'Enter Individual within Family Cap Amount',
                      validations: currencyValidation.optional(),
                    }
                  ),
                ]),
              ]
            : []),
          defineInlineFieldGroup([
            ...(productName === productNames.CMK_360 ||
            productName === productNames.ESI_360 ||
            productName === productNames.OPT_360 ||
            productName === productNames.IRX_360
              ? [
                  defineFormField(
                    'Max Allowable Cap',
                    'input',
                    `${basePath}.max_allowable_cap`,
                    initialData?.max_allowable_cap,
                    {
                      infoText: 'Max Allowable Cap',
                      placeholder: 'Enter Max Allowable Cap',
                      validations: currencyValidation.optional(),
                    }
                  ),
                ]
              : []),
            defineFormField(
              'CDH Class Code',
              'dropdownSelect',
              `${basePath}.cdh_class_code_ind`,
              initialData?.cdh_class_code_ind,
              {
                infoText: 'CDH Class Code',
                optionsMap: cdhClassCodeMap,
                validations: z.string().optional(),
              }
            ),
          ]),
          ...(productName === productNames.ESI_360
            ? [
                defineInlineFieldGroup([
                  defineFormField(
                    'Shared Indicator',
                    'dropdownSelect',
                    `${basePath}.shared_ind`,
                    initialData?.shared_ind,
                    {
                      infoText: 'Shared Indicator',
                      optionsMap: sharedIndicatorMap,
                    }
                  ),
                  defineFormField(
                    'Drug Type Status',
                    'dropdownSelect',
                    `${basePath}.drug_type_status_ind`,
                    initialData?.drug_type_status_ind,
                    {
                      infoText: 'Drug Type Status',
                      optionsMap: drugTypeStatusMap,
                    }
                  ),
                ]),
              ]
            : []),
          defineInlineFieldGroup([
            ...(productName === productNames.ESI_360
              ? [
                  defineFormField(
                    'Formulary Status',
                    'dropdownSelect',
                    `${basePath}.formulary_status_ind`,
                    initialData?.formulary_status_ind,
                    {
                      infoText: 'Formulary Status',
                      optionsMap: formularyStatusMap,
                    }
                  ),
                ]
              : []),
            ...(productName === productNames.CMK_360 ||
            productName === productNames.ESI_360 ||
            productName === productNames.OPT_360 ||
            productName === productNames.IRX_360
              ? [
                  defineFormField(
                    'Network Status',
                    'dropdownSelect',
                    `${basePath}.network_status_ind`,
                    initialData?.network_status_ind,
                    {
                      infoText: 'Network Status',
                      optionsMap: networkStatusMap,
                    }
                  ),
                ]
              : []),
          ]),
          ...(productName === productNames.ESI_360
            ? [
                defineInlineFieldGroup([
                  defineFormField(
                    'Pharmacy Channel',
                    'dropdownSelect',
                    `${basePath}.pharmacy_channel_ind`,
                    initialData?.pharmacy_channel_ind,
                    {
                      infoText: 'Pharmacy Channel',
                      optionsMap: pharmacyChannelAccumMap,
                    }
                  ),
                  defineFormField(
                    'Network Applicability',
                    'dropdownSelect',
                    `${basePath}.network_applicability_ind`,
                    currentData?.network_applicability_ind,
                    {
                      optionsMap: networkApplicabilityMap,
                      validations: smallintValidation,
                    }
                  ),
                ]),
                defineInlineFieldGroup([
                  defineFormField(
                    'Include Drug List',
                    'dropdownSelect',
                    `${basePath}.include_drug_list_ind`,
                    initialData?.include_drug_list_ind,
                    {
                      infoText: 'Include Drug List',
                      optionsMap: accumDrugListMap,
                    }
                  ),
                  defineFormField(
                    'Exclude Drug List',
                    'dropdownSelect',
                    `${basePath}.exclude_drug_list_ind`,
                    initialData?.exclude_drug_list_ind,
                    {
                      infoText: 'Exclude Drug List',
                      optionsMap: accumDrugListMap,
                    }
                  ),
                ]),
                defineInlineFieldGroup([
                  defineFormField(
                    'Other Cap Notes',
                    'textarea',
                    `${basePath}.notes`,
                    initialData?.notes,
                    {
                      infoText: 'Other Cap Notes',
                      placeholder: 'Enter Other Cap Notes',
                      validations: z
                        .string()
                        .max(2000, 'Value must be less than 2000 characters')
                        .optional()
                        .nullable(),
                      rows: 5,
                      customProps: {
                        minHeight: '120px',
                        overflow: 'hidden',
                      },
                    }
                  ),
                ]),
              ]
            : []),
        ]),
      ],
    };
  }, [
    accumDrugListMap,
    basePath,
    benefitPeriodLengthMap,
    benefitPeriodsMap,
    carryoverPhaseMap,
    cdhClassCodeMap,
    currentData,
    drugTypeStatusMap,
    esiAccumPeriodMap,
    formularyStatusMap,
    initialData,
    networkApplicabilityMap,
    otherAccumTypeMap,
    pharmacyChannelMap,
    productName,
    sharedIndicatorMap,
    yesNoMap,
  ]);

  return (
    <>
      <ConfirmationPopup
        isOpen={isOpen}
        onClose={onClose}
        onConfirm={onClose}
        alertHeader="Invalid Date Selection"
        alertBody={alertMessage}
        confirmButtonText="OK"
        hideCancelButton
      />

      <ModalBody>
        <GenericForm
          formMethods={formMethods}
          formName="Pharmacy Accumulators - Other Cap"
          formDescription="Here's a brief description placeholder of what this page will have the user work or make changes to. It should be able to provide context and information to the user to confidently answer information."
          subCategories={formConfig?.subCategories || []}
          showButtons={false} // Hide the built-in buttons
          isInModal
        />
      </ModalBody>
      <ModalFooter>
        <Button variant="outline" mr={3} onClick={onCancel}>
          Cancel
        </Button>
        <Button
          colorScheme="green"
          onClick={formMethods.handleSubmit(handleFormSubmit)}
          isDisabled={!isDateValid}
        >
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
