import {
  But<PERSON>,
  createS<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>lose<PERSON>utton,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { ExistingPlanDesigns } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/coreConfig';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { ChooseExistingPlanDesign } from './ChooseExistingPlanDesign';
import { SelectPlanDesignStart } from './SelectPlanDesignStart';

export const NewPlanDesignModal = ({
  isModalOpen,
  onModalClose,
  onCreateBlankPlanDesign,
  isCreateLoading,
  formMethods,
  existingPlanDesigns,
  areExistingPlanDesignsLoading,
  copyFromListIndex,
  setCopyFromListIndex,
}: {
  isModalOpen: boolean;
  onModalClose: () => void;
  onCreateBlankPlanDesign: () => Promise<void>;
  isCreateLoading: boolean;
  formMethods: UseFormReturn<any>;
  existingPlanDesigns: ExistingPlanDesigns;
  areExistingPlanDesignsLoading: boolean;
  copyFromListIndex?: string;
  setCopyFromListIndex: (value: any) => void;
}) => {
  const [selection, setSelection] = useState<
    'copyFromThisOrg' | 'copyFromOtherOrg' | 'blank' | undefined
  >(undefined);
  const [infoText, setInfoText] = useState<string>();
  const [organization, setOrganization] = useState<string | null>('');
  const [otherOrgPlanId, setOtherOrgPlanId] = useState<number>();
  const [isShowPlanDesigns, setIsShowPlanDesigns] = useState<boolean>(false);
  const [designsToCopy, setDesignsToCopy] = useState<(string | number)[]>(
    copyFromListIndex ? [copyFromListIndex] : []
  );
  const [nameMap, setNameMap] = useState<Record<string, string>>({});
  const [dateMap, setDateMap] = useState<Record<string, string>>({});
  const currentPlanDesigns = formMethods.getValues(PLAN_DESIGNS_BASE_PATH);

  const { useApiQuery, useApiMutation } = useBenAdmin();

  const saveHandler = useSaveChangeRequestHandler(formMethods, false, true);

  const { toast } = createStandaloneToast();

  const params = useParams();

  // need to init the state to contain this value if we're copying from listComponent
  useEffect(() => {
    if (!copyFromListIndex) {
      return;
    }
    setSelection('copyFromThisOrg');
    setDesignsToCopy([copyFromListIndex]);
    setIsShowPlanDesigns(true);
  }, [copyFromListIndex]);

  const { organization: organizations, isFetching: isOrgsLoading } =
    useApiQuery([
      {
        key: 'organization',
        queryParams: {
          pbm: formMethods.getValues('plan.product.vendor.name'),
        },
      },
    ]);

  const {
    plan: otherOrgPlanDesigns,
    isFetching: isOtherPlanLoading,
    refetch: { plan: fetchOtherOrgPlanDesigns },
  } = useApiQuery([
    {
      key: 'plan',
      pathParams: {
        orgId: organization,
      },
      queryParams: {
        type: 'organization_id',
        list: 'plan_designs',
      },
      options: { enabled: false },
    },
  ]);

  const { mutateAsync: copyPlanDesign, isPending: isCopying } = useApiMutation(
    'planDesign',
    'POST'
  );

  const onRadioChange = (value: string) => {
    if (value === 'blank') {
      setInfoText("Next: You'll start editing in the next screen.");
      setSelection('blank');
    } else if (value === 'copyFromThisOrg') {
      setInfoText('Select Plan Designs on next page.');
      setSelection('copyFromThisOrg');
    } else {
      setInfoText('Search for the organization you want to copy');
      setSelection('copyFromOtherOrg');
    }
    setOrganization(null);
  };

  const onContinue = async () => {
    if (selection === 'blank') {
      onCreateBlankPlanDesign();
    } else if (selection === 'copyFromOtherOrg') {
      const { data: otherOrgPlan } = await fetchOtherOrgPlanDesigns();
      if (otherOrgPlan?.plan_designs?.length > 0) {
        setIsShowPlanDesigns(true);
        setOtherOrgPlanId(otherOrgPlan.plan_designs[0].plan_id);
        setInfoText("Next: you'll start editing in the next screen.");
      } else {
        toast({
          title: 'No Plan Designs Available',
          description:
            'No Plan Designs are available in the selected organization. Please try another option.',
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      }
    } else {
      setInfoText('Make a selection to continue.');
      setIsShowPlanDesigns(true);
    }
  };

  const onBack = () => {
    if (!isShowPlanDesigns) {
      onModalClose();
      resetFormState();
      return;
    }
    setIsShowPlanDesigns(false);
    setDesignsToCopy([]);
    setNameMap({});
    setDateMap({});
    setInfoText('Make a selection to continue.');
    setCopyFromListIndex(undefined);
  };

  const resetFormState = () => {
    setSelection(undefined);
    setInfoText(undefined);
    setDesignsToCopy([]);
    setNameMap({});
    setDateMap({});
    setIsShowPlanDesigns(false);
    setOrganization(null);
    setOtherOrgPlanId(undefined);
    setCopyFromListIndex(undefined);
  };

  //   actual copy logic will go here
  const onCopyPlanDesign = async () => {
    if (selection === 'copyFromThisOrg') {
      const response = await copyPlanDesign({
        pathParams: {
          id: formMethods.getValues('plan.plan_id'),
        },
        queryParams: {
          change_request_id: params?.changeRequestId,
          plan_design_index: designsToCopy.join(','),
        },
      });

      proceedWithCopy(response);
    } else if (selection === 'copyFromOtherOrg') {
      const response = await copyPlanDesign({
        pathParams: { id: otherOrgPlanId },
        queryParams: {
          plan_design_id: designsToCopy.join(','),
        },
      });
      proceedWithCopy(response);
    }
    onModalClose();
    resetFormState();
  };

  const proceedWithCopy = async (response: any) => {
    const newCopiedPlanDesigns = response.map((planDesign: any) => ({
      ...planDesign,
      name: nameMap?.[planDesign.name],
      plan_design_details: planDesign.plan_design_details?.map(
        (detail: any) => ({
          ...detail,
          effective_date: dateMap?.[planDesign.name],
        })
      ),
    }));

    formMethods.setValue(PLAN_DESIGNS_BASE_PATH, [
      ...currentPlanDesigns,
      ...newCopiedPlanDesigns,
    ]);

    await saveHandler(formMethods.watch());
  };

  const getLoadingState = () => {
    if (selection === 'blank') return isCreateLoading;
    else if (selection === 'copyFromThisOrg')
      return areExistingPlanDesignsLoading;
    else if (selection === 'copyFromOtherOrg') return isOtherPlanLoading;
  };

  const areRequiredFieldsFilled = () => {
    // Only validate if user has selected items and is trying to proceed
    return designsToCopy.every((designKey) => {
      const planDesign =
        selection === 'copyFromThisOrg'
          ? existingPlanDesigns.plan_designs.find(
              (pd) => String(pd.plan_design_index) === String(designKey)
            )
          : (otherOrgPlanDesigns as ExistingPlanDesigns)?.plan_designs?.find(
              (pd) => String(pd.plan_design_id) === String(designKey)
            );

      if (!planDesign) return false;

      const hasName =
        nameMap[planDesign.name] && nameMap[planDesign.name].trim() !== '';
      const hasDate =
        dateMap[planDesign.name] && dateMap[planDesign.name].trim() !== '';

      return hasName && hasDate;
    });
  };

  const getDisabledState = () => {
    if (!selection) return true;
    else if (selection === 'copyFromOtherOrg' && !organization) return true;
    else if (isShowPlanDesigns && designsToCopy.length === 0) return true;
    else if (isShowPlanDesigns && !areRequiredFieldsFilled()) return true;
    else return false;
  };

  return (
    <Modal
      isOpen={isModalOpen}
      onClose={() => {
        onModalClose();
        resetFormState();
      }}
      size="2xl"
      isCentered
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Text fontSize="lg" color="#69696A">
            Add New Plan Design(s)
          </Text>
        </ModalHeader>
        <ModalCloseButton />
        <Divider />
        <ModalBody>
          {isShowPlanDesigns ? (
            <ChooseExistingPlanDesign
              planDesigns={
                selection === 'copyFromThisOrg'
                  ? existingPlanDesigns
                  : (otherOrgPlanDesigns as ExistingPlanDesigns)
              }
              selection={selection}
              designsToCopy={designsToCopy}
              setDesignsToCopy={setDesignsToCopy}
              setNameMap={setNameMap}
              setDateMap={setDateMap}
              nameMap={nameMap}
              dateMap={dateMap}
            />
          ) : (
            <>
              <SelectPlanDesignStart
                onRadioChange={onRadioChange}
                selection={selection}
                organization={organization}
                setOrganization={setOrganization}
                currentOrgPlanDesignCount={currentPlanDesigns?.length}
                allOrgsByPBM={organizations as any}
                isOrgsLoading={isOrgsLoading}
              />
              <Divider />
            </>
          )}
        </ModalBody>
        <ModalFooter display="flex" justifyContent="space-between">
          <Text>{infoText ?? 'Make a selection to continue.'}</Text>
          <Flex justify="space-between" gap={3}>
            <Button variant="outline" onClick={onBack}>
              Back
            </Button>
            <Button
              colorScheme="green"
              isDisabled={getDisabledState()}
              onClick={isShowPlanDesigns ? onCopyPlanDesign : onContinue}
              isLoading={getLoadingState() || isCopying}
            >
              Continue
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
