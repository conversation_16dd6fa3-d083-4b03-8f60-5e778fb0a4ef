import { Box, Icon, Text } from '@chakra-ui/react';
import React from 'react';
import { PiWarningOctagonFill } from 'react-icons/pi';

interface ValidationErrorIndicatorProps {
  errorCount: number;
  debug?: boolean;
}

export const ValidationErrorIndicator: React.FC<
  ValidationErrorIndicatorProps
> = ({ errorCount, debug = false }) => {
  if (errorCount === 0) return null;

  return (
    <Box
      bg="#FEF0EF"
      borderRadius="8px"
      px={3}
      py={1}
      ml={2}
      display="flex"
      alignItems="center"
      data-testid="validation-error-indicator"
    >
      <Icon as={PiWarningOctagonFill} color="#DF5D53" boxSize={4} mr={2} />
      <Text fontSize="sm" fontWeight="medium" color="#873832">
        Error {errorCount}
        {debug ? ' (all)' : ''}
      </Text>
    </Box>
  );
};
