import { Box } from '@chakra-ui/react';
import { SidebarConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { IntakeHeader } from './IntakeHeader';

interface MainContentProps {
  formMethods: UseFormReturn<any>;
  currentComponent: React.ReactNode;
  currentMode: string;
  sidebarConfig: SidebarConfig;
  activeItem: string;
  onBreadcrumbNavigate: (id: string) => void;
}

const MainContent: React.FC<MainContentProps> = ({
  formMethods,
  currentComponent,
  currentMode,
  sidebarConfig,
  activeItem,
  onBreadcrumbNavigate,
}) => {
  // Generate breadcrumb paths based on current state
  const getBreadcrumbPaths = () => {
    const paths = [{ label: 'Project Overview', id: 'preview' }];

    // For main mode, just add Dashboard and return
    if (currentMode === 'main') {
      paths.unshift({ label: 'Dashboard', id: 'dashboard' });
      return paths;
    }

    const items = sidebarConfig?.sections?.[0]?.items || [];

    // Search for active item in both top-level and nested items
    for (const item of items) {
      // Case 1: Match at top level
      if (item.id === activeItem && item.component) {
        paths.push({ label: item.label, id: item.id });
        return paths;
      }

      // Case 2: Match in dropdown
      if (item.hasDropdown && item.dropdownItems) {
        const nestedItem = item.dropdownItems.find(
          (nested) => nested.id === activeItem && nested.component
        );

        if (nestedItem) {
          paths.push({ label: item.label, id: '' }); // Parent with empty id for non-clickable
          paths.push({ label: nestedItem.label, id: nestedItem.id }); // Child (clickable)
          return paths;
        }
      }
    }

    return paths;
  };

  return (
    <Box flex="1" display="flex" flexDirection="column" overflow="hidden">
      <Box w="97%" mx="auto" borderRadius="lg">
        <IntakeHeader
          formMethods={formMethods}
          getBreadcrumbPaths={getBreadcrumbPaths}
          onBreadcrumbNavigate={onBreadcrumbNavigate}
        />
      </Box>
      <Box flex="1" w="98%" mx="auto" overflowY="auto" p={4}>
        {currentComponent}
      </Box>
    </Box>
  );
};

export default MainContent;
