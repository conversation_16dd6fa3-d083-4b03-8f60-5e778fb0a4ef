import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getPathParams } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { NavigationConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';

import {
  generateFeatureTabId,
  getFirstRelevantFeature,
} from '../../../hooks/Configurable/featureUtils';
import { getClientProfileSidebarConfig } from '../MenuItems/PlanDesignTool/ClientProfile/clientProfileSidbarConfig';
import { getDynamicClinicalDesignConfig } from '../MenuItems/PlanDesignTool/ClinicalDesigns/clinicalDesignsSidebarConfig';
import { getMemberServicesSidebarConfig } from '../MenuItems/PlanDesignTool/MemberExperience/memberExperienceSidebarConfig';
import { getPlanDesignSidebarConfig } from '../MenuItems/PlanDesignTool/PlanDesign/planDesignSidebarConfig';
import { getProductsAndServicesSidebarConfig } from '../MenuItems/PlanDesignTool/ProductsAndServices/productsAndServicesSidebarConfig';
import { getMainSidebarConfig } from './mainSidebarConfig';
import {
  CLIENT_INFORMATION_ITEM,
  CLIENT_PROFILE_MODE,
  CLINICAL_DESIGN_MODE,
  ID_CARDS_ITEM,
  MAIN_MODE,
  MEMBER_EXPERIENCE_MODE,
  PLAN_DESIGN_LIST_ITEM,
  PLAN_DESIGN_MODE,
  PRODUCT_SET_UP_ITEM,
  PRODUCTS_AND_SERVICES_MODE,
} from './navigationConstants';

/**
 * Generates the navigation configuration for the application.
 * Uses shared feature ID generation for clinical design mode.
 *
 * @param baseUrl - The base URL for navigation.
 * @param organizationDetails - Data about the organization, passed to components.
 * @param isESI - Optional flag indicating if the product is ESI-specific; defaults to false.
 * @param formMethods - Form handling methods from react-hook-form, passed to components.
 * @returns A NavigationConfig object defining the navigation structure.
 */
export const getNavigationConfig = (
  baseUrl: string,
  organizationDetails: OrganizationDetails,
  formMethods: UseFormReturn<any>,
  isESI: boolean
): NavigationConfig => {
  // Get the first plan design's features from form values and find the first relevant one
  const firstPlanFeatures =
    formMethods.getValues('plan.plan_designs.0.plan_features') || [];

  // Use shared utility to get the first relevant feature
  const firstRelevantFeature = getFirstRelevantFeature(
    firstPlanFeatures,
    organizationDetails.features
  );

  // Generate ID for first feature
  const firstFeatureId = firstRelevantFeature
    ? generateFeatureTabId(firstRelevantFeature)
    : 'CLINICAL_DESIGN_ITEM';

  const urlObjectToKeep = ['index', 'title', 'edit', 'emptyState'];

  return {
    baseUrl,
    organizationDetails,
    formMethods,
    defaultMode: MAIN_MODE,
    modes: {
      [MAIN_MODE]: {
        getSidebarConfig: getMainSidebarConfig,
        defaultItemId: 'preview',
      },
      [CLIENT_PROFILE_MODE]: {
        getSidebarConfig: getClientProfileSidebarConfig,
        defaultItemId: CLIENT_INFORMATION_ITEM,
      },
      [MEMBER_EXPERIENCE_MODE]: {
        getSidebarConfig: getMemberServicesSidebarConfig,
        defaultItemId: ID_CARDS_ITEM,
      },
      [PLAN_DESIGN_MODE]: {
        getSidebarConfig: (orgDetails, formMethods, navigateFn, activeItemId) =>
          getPlanDesignSidebarConfig(
            orgDetails,
            formMethods,
            isESI ?? false,
            navigateFn,
            activeItemId
          ),
        defaultItemId: PLAN_DESIGN_LIST_ITEM,
        urlParams: getPathParams(urlObjectToKeep),
      },
      [PRODUCTS_AND_SERVICES_MODE]: {
        getSidebarConfig: getProductsAndServicesSidebarConfig,
        defaultItemId: PRODUCT_SET_UP_ITEM,
        urlParams: getPathParams(urlObjectToKeep),
      },
      [CLINICAL_DESIGN_MODE]: {
        getSidebarConfig: getDynamicClinicalDesignConfig,
        defaultItemId: firstFeatureId,
        urlParams: getPathParams(urlObjectToKeep),
      },
    },
  };
};
