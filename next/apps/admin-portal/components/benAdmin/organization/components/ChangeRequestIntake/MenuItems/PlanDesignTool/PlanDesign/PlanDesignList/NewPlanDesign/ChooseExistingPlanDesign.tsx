import {
  Box,
  Checkbox,
  CheckboxGroup,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Tag,
  Text,
  VStack,
} from '@chakra-ui/react';
import { ExistingPlanDesigns } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { formatDate } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import { DateControl } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Template/DateControl';
import { useState } from 'react';

export const ChooseExistingPlanDesign = ({
  planDesigns,
  selection = 'copyFromThisOrg',
  designsToCopy,
  setDesignsToCopy,
  setNameMap,
  setDateMap,
  nameMap,
  dateMap,
}: {
  planDesigns: ExistingPlanDesigns;
  selection: 'copyFromThisOrg' | 'copyFromOtherOrg' | 'blank' | undefined;
  designsToCopy: (string | number)[];
  setDesignsToCopy: (values: (string | number)[]) => void;
  setNameMap: (value: any) => void;
  setDateMap: (value: any) => void;
  nameMap: Record<string, string>;
  dateMap: Record<string, string>;
}) => {
  const { plan_designs: planDesignsAvailable } = planDesigns;
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  const markFieldAsTouched = (
    planDesignName: string,
    fieldType: 'name' | 'date'
  ) => {
    setTouchedFields((prev) =>
      new Set(prev).add(`${planDesignName}-${fieldType}`)
    );
  };

  const isFieldTouched = (
    planDesignName: string,
    fieldType: 'name' | 'date'
  ) => {
    return touchedFields.has(`${planDesignName}-${fieldType}`);
  };

  return (
    <VStack spacing={9} align="stretch">
      <Text fontSize="2xl" color="#022F6C">
        Which Plan Design(s) do you want to copy?
      </Text>

      <Flex direction="column" color="#69696A">
        <Flex gap={3}>
          <Text fontWeight={500}>
            Which Plan Design(s) do you want to copy?
          </Text>
          <Tag colorScheme="cyan">{planDesignsAvailable.length} Available</Tag>
          <Tag colorScheme="orange">{designsToCopy.length} Selected</Tag>
        </Flex>
        <Box mt={2} p={3} borderRadius="8px" border="1px solid #EBEBEC">
          <CheckboxGroup
            colorScheme="green"
            value={designsToCopy}
            onChange={(value) => setDesignsToCopy(value)}
          >
            <VStack align="start">
              {planDesignsAvailable.map((planDesign, index) => {
                const checkboxValue =
                  selection === 'copyFromThisOrg'
                    ? String(planDesign.plan_design_index)
                    : String(planDesign.plan_design_id);
                const isChecked = designsToCopy.includes(checkboxValue);

                return (
                  <Box key={checkboxValue} w="100%">
                    {index !== 0 && <Divider />}
                    <Checkbox py={2} value={checkboxValue}>
                      {planDesign.name}
                    </Checkbox>
                    {isChecked && (
                      <Flex justify="space-between" w="100%" gap={6}>
                        <FormControl
                          isRequired
                          isInvalid={
                            isFieldTouched(planDesign.name, 'name') &&
                            (!nameMap[planDesign.name] ||
                              nameMap[planDesign.name]?.trim() === '')
                          }
                        >
                          <FormLabel>Plan Design Name</FormLabel>
                          <Input
                            placeholder="Enter a Plan Design Name"
                            onChange={(e) => {
                              markFieldAsTouched(planDesign.name, 'name');
                              // need to map old name to new in order to match these inputs to the newly copied plan designs
                              // same with date below
                              setNameMap((prev: any) => ({
                                ...prev,
                                [planDesign.name]: e.target.value,
                              }));
                            }}
                          />
                          <FormErrorMessage>
                            {isFieldTouched(planDesign.name, 'name') &&
                              (!nameMap[planDesign.name] ||
                                nameMap[planDesign.name]?.trim() === '') &&
                              'Plan Design Name is required'}
                          </FormErrorMessage>
                        </FormControl>
                        <FormControl
                          w="35%"
                          isRequired
                          isInvalid={
                            isFieldTouched(planDesign.name, 'date') &&
                            (!dateMap[planDesign.name] ||
                              dateMap[planDesign.name]?.trim() === '')
                          }
                        >
                          <FormLabel>Effective Date</FormLabel>
                          <DateControl
                            onChange={(value) => {
                              markFieldAsTouched(planDesign.name, 'date');
                              setDateMap((prev: any) => ({
                                ...prev,
                                [planDesign.name]: formatDate(value, true),
                              }));
                            }}
                          />
                          <FormErrorMessage>
                            {isFieldTouched(planDesign.name, 'date') &&
                              (!dateMap[planDesign.name] ||
                                dateMap[planDesign.name]?.trim() === '') &&
                              'Effective Date is required'}
                          </FormErrorMessage>
                        </FormControl>
                      </Flex>
                    )}
                  </Box>
                );
              })}
            </VStack>
          </CheckboxGroup>
        </Box>
      </Flex>
    </VStack>
  );
};
