import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { SidebarConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';

import {
  ACCUMULATORS_GENERAL_ITEM,
  COMPOUND_ITEM,
  DEDUCTIBLE_ITEM,
  DISPENSE_ITEM,
  GENERAL_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
  OTHER_CAP_ITEM,
  PATIENT_PAY_ITEM,
  PHARMACY_ACCUMULATORS_ITEM,
  PLAN_DESIGN_LIST_ITEM,
  PLAN_DESIGN_MODE,
  PLAN_DESIGN_REVIEW_ITEM,
  <PERSON><PERSON><PERSON><PERSON>_ITEM,
  UNBREAKABLE_ITEM,
  XML_ITEM,
} from '../../../Navigation/navigationConstants';
import GeneralComponent from './General/generalComponent';
import CompoundComponent from './PatientPay/Compound/compoundComponent';
import StandardComponent from './PatientPay/CostShare/Standard/standardComponent';
import UnbreakableComponent from './PatientPay/CostShare/Unbreakable/unbreakableComponent';
import DispenseComponent from './PatientPay/Dispense/dispenseComponent';
import DeductibleComponent from './PharmacyAccumulators/Deductible/deductibleComponent';
import AccumsGeneralComponent from './PharmacyAccumulators/General/accumsGeneralComponent';
import MoopComponent from './PharmacyAccumulators/MaximumOutOfPocket/moopComponet';
import OtherCapComponent from './PharmacyAccumulators/OtherCap/otherCapComponent';
import PlanListComponent from './PlanDesignList/listComponent';
import PlanDesignReview from './Review/planDesignReview';
import XMLComponent from './XML/xmlComponent';
// Import the utility function instead of the hook

export const getPlanDesignSidebarConfig = (
  changeRequest: OrganizationDetails,
  formMethods: UseFormReturn<any>,
  isESI?: boolean, // Add isESI as a parameter
  navigateFn?: (section: string, tab: string) => void,
  activeItemId?: string
): SidebarConfig => {
  const navigateToClientProfileItem = (id: string) =>
    navigateFn ? navigateFn(PLAN_DESIGN_MODE, id) : undefined;

  // Check if we're currently on the Plan Design List - if so, disable all other items
  const isOnPlanDesignList = activeItemId === PLAN_DESIGN_LIST_ITEM;

  return {
    sections: [
      {
        title: 'PLAN DESIGN',
        items: [
          {
            id: PLAN_DESIGN_LIST_ITEM,
            label: 'Plan Design List',
            component: (
              <PlanListComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: GENERAL_ITEM,
            label: 'General',
            component: (
              <GeneralComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
            disabled: isOnPlanDesignList,
          },
          // Only include XML_ITEM if isESI is true
          ...(isESI ?? false // Default to false if isESI is undefined
            ? [
                {
                  id: XML_ITEM,
                  label: 'XML Leave Behind - Info Provided by ESI',
                  component: (
                    <XMLComponent
                      formMethods={formMethods}
                      onUpdateActiveItem={navigateToClientProfileItem}
                    />
                  ),
                  disabled: isOnPlanDesignList,
                },
              ]
            : []),
          {
            id: PATIENT_PAY_ITEM,
            label: 'Patient Pay',
            hasDropdown: true,
            disabled: isOnPlanDesignList,
            dropdownItems: [
              {
                id: STANDARD_ITEM,
                label: 'Standard',
                component: (
                  <StandardComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
              {
                id: UNBREAKABLE_ITEM,
                label: 'Unbreakable',
                component: (
                  <UnbreakableComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
              {
                id: COMPOUND_ITEM,
                label: 'Compounds',
                component: (
                  <CompoundComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
              {
                id: DISPENSE_ITEM,
                label: 'Dispense as Written',
                component: (
                  <DispenseComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
            ],
          },
          {
            id: PHARMACY_ACCUMULATORS_ITEM,
            label: 'Pharmacy Accumulators',
            hasDropdown: true,
            disabled: isOnPlanDesignList,
            dropdownItems: [
              {
                id: ACCUMULATORS_GENERAL_ITEM,
                label: 'General',
                component: (
                  <AccumsGeneralComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
              {
                id: DEDUCTIBLE_ITEM,
                label: 'Deductible',
                component: (
                  <DeductibleComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
              {
                id: MAXIMUM_OUT_OF_POCKET_ITEM,
                label: 'Maximum Out of Pocket',
                component: (
                  <MoopComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
              {
                id: OTHER_CAP_ITEM,
                label: 'Other Cap',
                component: (
                  <OtherCapComponent
                    formMethods={formMethods}
                    onUpdateActiveItem={navigateToClientProfileItem}
                  />
                ),
              },
            ],
          },
          {
            id: PLAN_DESIGN_REVIEW_ITEM,
            label: 'Review',
            disabled: isOnPlanDesignList,
            component: (
              <PlanDesignReview
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
        ],
      },
    ],
  };
};
