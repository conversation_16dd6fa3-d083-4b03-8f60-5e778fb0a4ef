import { InlineEditColumn } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/FormTable';
import { useMemo } from 'react';

import {
  formatCurrency,
  formatDate,
  formatPercentage,
  formatYesNo,
} from './utils/costShareUtils';

/**
 * Pure function to generate cost share columns, optionally read-only.
 */
export function getCostShareColumns(
  maps: any,
  isESI: boolean,
  columnWidth = '200px',
  readOnly = false,
  showTierType = false
): InlineEditColumn[] {
  const editable = (def: boolean) => (readOnly ? false : def);
  const baseColumns: InlineEditColumn[] = [
    {
      key: 'name',
      header: 'Tier Name',
      editable: false,
      editType: 'text',
      placeholder: 'Set by system...',
      width: '140px',
    },
    ...(showTierType
      ? [
          {
            key: 'pre_packaged_ind',
            header: 'Type',
            editable: false,
            editType: 'text' as const,
            placeholder: '--',
            width: '80px',
            format: (value: any) => {
              const numValue = Number(value);
              return numValue === 1 ? 'Unbreakable' : 'Standard';
            },
          },
        ]
      : []),
    {
      key: 'pbc_print_order',
      header: 'PBC Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Order...',
      width: '80px',
      format: (value: any) => value || '--',
    },
    {
      key: 'pdx_print_order',
      header: 'PDX Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Order...',
      width: '80px',
      format: (value: any) => value || '--',
    },
    {
      key: 'effective_date',
      header: 'Effective',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Date',
      width: '100px',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'expiration_date',
      header: 'Expiration',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Date',
      width: '100px',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'tier_ind',
      header: 'Tier',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierMap,
      searchable: true,
      placeholder: 'Select',
      width: '90px',
      format: (value: any) =>
        value ? `${maps.costShareTierMap?.[value] || value}` : '--',
    },
    {
      key: 'pharmacy_channel_ind',
      header: 'Channel',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.pharmacyChannelMap,
      searchable: true,
      placeholder: 'Select',
      width: '90px',
      format: (value: any) => maps.pharmacyChannelMap?.[value] || value || '--',
    },
    {
      key: 'days_supply',
      header: 'Days',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierDaysSupplyMap,
      placeholder: 'Select',
      width: '70px',
      format: (value: any) =>
        maps.costShareTierDaysSupplyMap?.[value] || value || '--',
    },
    {
      key: 'co_pay_amount',
      header: 'Copay',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Amount',
      width: '80px',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_pct',
      header: 'Coins %',
      editable: editable(true),
      editType: 'number',
      placeholder: '%',
      width: '70px',
      format: (value: any) => formatPercentage(value),
    },
    {
      key: 'co_insurance_ltgt_ind',
      header: 'L/G',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.coInsuranceLesserGreaterMap,
      placeholder: 'Select',
      width: '60px',
      format: (value: any) =>
        maps.coInsuranceLesserGreaterMap?.[value] || value || '--',
    },
    {
      key: 'co_insurance_min_amount',
      header: 'Min',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Min',
      width: '70px',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_max_amount',
      header: 'Max',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Max',
      width: '70px',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'network_status_ind',
      header: 'Network',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.networkStatusMap,
      placeholder: 'Select',
      width: '90px',
      format: (value: any) =>
        value ? `${maps.networkStatusMap?.[value] || '--'}` : '--',
    },
    {
      key: 'pre_packaged_ind',
      header: 'Prepackage',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select',
      width: '90px',
      format: (value: any) => maps.yesNoMap?.[value] || value || '--',
    },
    // ESI-only fields
    ...(isESI
      ? [
          {
            key: 'drug_list_ind',
            header: 'Drug List',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiDrugListMap,
            searchable: true,
            placeholder: 'Select',
            width: '100px',
            format: (value: any) =>
              maps.esiDrugListMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_type_ind',
            header: 'Copay Type',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayTierMap,
            placeholder: 'Select',
            width: '100px',
            format: (value: any) =>
              maps.esiCopayTierMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_channel_ind',
            header: 'Channel Delivery',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayChannelMap,
            placeholder: 'Select',
            width: '120px',
            format: (value: any) =>
              maps.esiCopayChannelMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_structure_ind',
            header: 'Structure',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayStructureMap,
            placeholder: 'Select',
            width: '100px',
            format: (value: any) =>
              maps.esiCopayStructureMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_network_ind',
            header: 'Network',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayNetworkMap,
            placeholder: 'Select',
            width: '90px',
            format: (value: any) =>
              maps.esiCopayNetworkMap?.[value] || value || '--',
          },
        ]
      : []),
    {
      key: 'include_in_pbc',
      header: 'Inc PBC',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select',
      width: '70px',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
    {
      key: 'include_in_pdx',
      header: 'Inc PDX',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select',
      width: '70px',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
  ];
  // Return columns without fixed width to allow automatic sizing based on content
  return baseColumns;
}

/**
 * React hook to memoize cost share columns for editable tables.
 */
export function useCostShareColumns(
  maps: any,
  isESI: boolean,
  showTierType = false
): InlineEditColumn[] {
  return useMemo(
    () => getCostShareColumns(maps, isESI, '200px', false, showTierType),
    [maps, isESI, showTierType]
  );
}
