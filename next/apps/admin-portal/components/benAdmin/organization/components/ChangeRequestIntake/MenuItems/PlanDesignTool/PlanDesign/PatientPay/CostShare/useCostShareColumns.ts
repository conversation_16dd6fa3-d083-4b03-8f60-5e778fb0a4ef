import { InlineEditColumn } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/FormTable';
import { useMemo } from 'react';

import {
  formatCurrency,
  formatDate,
  formatPercentage,
  formatYesNo,
} from './utils/costShareUtils';

/**
 * Pure function to generate cost share columns, optionally read-only.
 */
export function getCostShareColumns(
  maps: any,
  isESI: boolean,
  columnWidth?: string,
  readOnly = false,
  showTierType = false
): InlineEditColumn[] {
  const editable = (def: boolean) => (readOnly ? false : def);
  const baseColumns: InlineEditColumn[] = [
    {
      key: 'name',
      header: 'Cost Share Tier Name',
      editable: false,
      editType: 'text',
      placeholder: 'Set by system...',
      width: '50px',
    },
    ...(showTierType
      ? [
          {
            key: 'pre_packaged_ind',
            header: 'Tier Type',
            editable: false,
            editType: 'text' as const,
            placeholder: '--',
            width: '50px',
            format: (value: any) => {
              const numValue = Number(value);
              return numValue === 1 ? 'Unbreakable' : 'Standard';
            },
          },
        ]
      : []),
    {
      key: 'pbc_print_order',
      header: 'PBC Print Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter order...',
      width: '50px',
      format: (value: any) => value || '--',
    },
    {
      key: 'pdx_print_order',
      header: 'PDX Print Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter order...',
      width: '50px',
      format: (value: any) => value || '--',
    },
    {
      key: 'effective_date',
      header: 'Effective Date',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Select date',
      width: '50px',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'expiration_date',
      header: 'Expiration Date',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Select date',
      width: '50px',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'tier_ind',
      header: 'Drug Tier',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierMap,
      searchable: true,
      placeholder: 'Select tier...',
      width: '50px',
      format: (value: any) =>
        value ? `${maps.costShareTierMap?.[value] || value}` : '--',
    },
    {
      key: 'pharmacy_channel_ind',
      header: 'Pharmacy Channel',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.pharmacyChannelMap,
      searchable: true,
      placeholder: 'Select channel...',
      width: '50px',
      format: (value: any) => maps.pharmacyChannelMap?.[value] || value || '--',
    },
    {
      key: 'days_supply',
      header: 'Days Supply',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierDaysSupplyMap,
      placeholder: 'Select days...',
      width: '50px',
      format: (value: any) =>
        maps.costShareTierDaysSupplyMap?.[value] || value || '--',
    },
    {
      key: 'co_pay_amount',
      header: 'Copay Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter amount...',
      width: '50px',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_pct',
      header: 'Coinsurance %',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter %...',
      width: '50px',
      format: (value: any) => formatPercentage(value),
    },
    {
      key: 'co_insurance_ltgt_ind',
      header: 'Lesser/Greater',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.coInsuranceLesserGreaterMap,
      placeholder: 'Select...',
      width: '50px',
      format: (value: any) =>
        maps.coInsuranceLesserGreaterMap?.[value] || value || '--',
    },
    {
      key: 'co_insurance_min_amount',
      header: 'Min Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Min amount...',
      width: '50px',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_max_amount',
      header: 'Max Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Max amount...',
      width: '50px',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'network_status_ind',
      header: 'Network Status',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.networkStatusMap,
      placeholder: 'Select status...',
      width: '90px',
      format: (value: any) =>
        value ? `${maps.networkStatusMap?.[value] || '--'}` : '--',
    },
    {
      key: 'pre_packaged_ind',
      header: 'Prepackage Indicator',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      width: '90px',
      format: (value: any) => maps.yesNoMap?.[value] || value || '--',
    },
    // ESI-only fields
    ...(isESI
      ? [
          {
            key: 'drug_list_ind',
            header: 'Drug List',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiDrugListMap,
            searchable: true,
            placeholder: 'Select list...',
            width: '100px',
            format: (value: any) =>
              maps.esiDrugListMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_type_ind',
            header: 'Copay Type Indicator',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayTierMap,
            placeholder: 'Select type...',
            width: '100px',
            format: (value: any) =>
              maps.esiCopayTierMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_channel_ind',
            header: 'Copay Channel Delivery System',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayChannelMap,
            placeholder: 'Select channel...',
            width: '120px',
            format: (value: any) =>
              maps.esiCopayChannelMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_structure_ind',
            header: 'Copay Structure',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayStructureMap,
            placeholder: 'Select structure...',
            width: '100px',
            format: (value: any) =>
              maps.esiCopayStructureMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_network_ind',
            header: 'Copay Network',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayNetworkMap,
            placeholder: 'Select network...',
            width: '90px',
            format: (value: any) =>
              maps.esiCopayNetworkMap?.[value] || value || '--',
          },
        ]
      : []),
    {
      key: 'include_in_pbc',
      header: 'Include PBC',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      width: '70px',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
    {
      key: 'include_in_pdx',
      header: 'Include in PDX',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      width: '70px',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
  ];

  // If columnWidth is provided, override all column widths with the specified value
  // Otherwise, use the individual column widths defined above
  if (columnWidth) {
    return baseColumns.map((column) => ({ ...column, width: columnWidth }));
  }

  // Return columns with their individual widths
  return baseColumns;
}

/**
 * React hook to memoize cost share columns for editable tables.
 */
export function useCostShareColumns(
  maps: any,
  isESI: boolean,
  showTierType = false
): InlineEditColumn[] {
  return useMemo(
    () => getCostShareColumns(maps, isESI, undefined, false, showTierType),
    [maps, isESI, showTierType]
  );
}
