import { useCallback } from 'react';

interface UseModalFormValidationProps {
  refetch: () => Promise<any>;
  navigationConstant: string;
  getUIContextFromNavigationConstant: (constant: string) => any;
  planDesignIndex: number;
  itemIndex: number;
  isNewItem: boolean;
  basePath: string;
  formMethods: any;
  fieldIndexPosition?: number;
}

export const useModalFormValidation = (props: UseModalFormValidationProps) => {
  const {
    refetch,
    navigationConstant,
    getUIContextFromNavigationConstant,
    planDesignIndex,
    itemIndex,
    isNewItem,
    basePath,
    formMethods,
    fieldIndexPosition = 5,
  } = props;

  const validateModal = useCallback(async (): Promise<boolean> => {
    const result = await refetch();
    await new Promise((resolve) => setTimeout(resolve, 100));

    const uiContextInd = getUIContextFromNavigationConstant(navigationConstant);
    const pageResult = uiContextInd
      ? result?.data?.results?.[uiContextInd]
      : null;

    if (!pageResult) return false;

    const pageErrors = [
      ...(pageResult?.errors || []),
      ...(pageResult?.validation_results?.errors || []),
    ];
    const pageWarnings = [
      ...(pageResult?.warnings || []),
      ...(pageResult?.validation_results?.warnings || []),
    ];

    const relevantErrors = pageErrors.filter(({ field }) => {
      const fieldParts = field.split('.');
      const apiPlanDesignIndex = fieldParts[1];
      const apiItemIndex = fieldParts[fieldIndexPosition];
      return (
        apiPlanDesignIndex === planDesignIndex.toString() &&
        (apiItemIndex === itemIndex.toString() ||
          (isNewItem && apiItemIndex === '0'))
      );
    });

    const relevantWarnings = pageWarnings.filter(({ field }) => {
      const fieldParts = field.split('.');
      const apiPlanDesignIndex = fieldParts[1];
      const apiItemIndex = fieldParts[fieldIndexPosition];
      return (
        apiPlanDesignIndex === planDesignIndex.toString() &&
        (apiItemIndex === itemIndex.toString() ||
          (isNewItem && apiItemIndex === '0'))
      );
    });

    if (relevantErrors.length > 0 || relevantWarnings.length > 0) {
      formMethods.clearErrors();

      relevantErrors.forEach(({ field, message }) => {
        const fieldParts = field.split('.');
        const fieldName = fieldParts[fieldParts.length - 1];
        const modalFieldPath = `${basePath}.${fieldName}`;

        if (message) {
          formMethods.setError(modalFieldPath, {
            type: 'field-error',
            message,
          });
        }
      });

      relevantWarnings.forEach(({ field, message }) => {
        const fieldParts = field.split('.');
        const fieldName = fieldParts[fieldParts.length - 1];
        const modalFieldPath = `${basePath}.${fieldName}`;

        if (message) {
          formMethods.setError(modalFieldPath, {
            type: 'field-warning',
            message,
          });
        }
      });

      return true;
    }

    return false;
  }, [
    refetch,
    navigationConstant,
    getUIContextFromNavigationConstant,
    planDesignIndex,
    itemIndex,
    isNewItem,
    basePath,
    formMethods,
    fieldIndexPosition,
  ]);

  return validateModal;
};
