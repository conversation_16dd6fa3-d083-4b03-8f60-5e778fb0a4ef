import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  PLAN_DESIGNS_BASE_PATH,
  PlanDesign,
} from 'apps/admin-portal/components/benAdmin';
import { syncPlanDesignStructure } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/MenuItems/PlanDesignTool/ClinicalDesigns/DynamicForm/paramsUtils';
import { useEffect, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';

/**
 * Hook to sync plan design structure when formMethods is first created
 * This ensures all plan designs have consistent structure based on the latest API response
 */
export const usePlanDesignSync = (formMethods: UseFormReturn<any>) => {
  const { useApiMutation } = useBenAdmin();
  const hasRunRef = useRef(false);

  // Use mutation for creating plan design to get fresh structure
  const { mutateAsync: createPlanDesign } = useApiMutation(
    'planDesign',
    'POST',
    {
      // Request for complete data structure in response
      include: ['plan_design_details', 'plan_features'],
    }
  );

  useEffect(() => {
    // Only run once per formMethods instance
    if (hasRunRef.current) return;

    const syncPlanDesignStructures = async () => {
      try {
        const plan_id = formMethods.getValues('plan.plan_id');
        if (!plan_id) return;

        // Get existing plan designs from form
        const existingPlanDesigns =
          formMethods.getValues(PLAN_DESIGNS_BASE_PATH) || [];

        // If no existing plan designs, nothing to sync
        if (existingPlanDesigns.length === 0) return;

        // Get fresh plan design structure from API
        const response = await createPlanDesign({
          pathParams: { id: plan_id },
          plan_id,
        });

        if (!response?.plan_designs?.[0]) return;

        const convertZerosToNulls = (obj: any): any => {
          if (obj === null || obj === undefined) return obj;
          if (obj === 0) return null;

          if (typeof obj !== 'object') return obj;

          if (Array.isArray(obj)) {
            return obj.map((item) => convertZerosToNulls(item));
          }

          const result: Record<string, any> = {};
          for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
              result[key] = convertZerosToNulls(obj[key]);
            }
          }

          return result;
        };

        const rawPlanDesign = response.plan_designs[0];
        const sourcePlanDesign = convertZerosToNulls(
          rawPlanDesign
        ) as PlanDesign;

        // Create a copy of existing plan designs to sync
        const syncedPlanDesigns = [...existingPlanDesigns];

        // Sync structure of all existing plan designs to match the source
        syncedPlanDesigns.forEach((currentDesign: PlanDesign) => {
          syncPlanDesignStructure(currentDesign, sourcePlanDesign);
        });

        // Update the form with synced plan designs
        formMethods.setValue(PLAN_DESIGNS_BASE_PATH, syncedPlanDesigns, {
          shouldValidate: false,
          shouldDirty: false,
        });

        console.log('Plan design structures synced successfully');
      } catch (error) {
        console.error('Failed to sync plan design structures:', error);
        // Don't throw error to avoid breaking the form initialization
      }
    };

    syncPlanDesignStructures();
    hasRunRef.current = true;
  }, [formMethods, createPlanDesign]);
};
