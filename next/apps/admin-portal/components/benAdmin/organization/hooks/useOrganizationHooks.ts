// --------------------------------------------------
// useOrganizationHooks.ts - Hook for Handling Save Changes
// --------------------------------------------------

import { createStandaloneToast } from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { OrganizationDetails } from '../../Models/interfaces';

// --------------------------------------------------
// 1) Type Definitions
// --------------------------------------------------

/**
 * Represents the structure of additional parameters for REST API calls.
 */
type RestParams = {
  [key: string]: number | string;
};

// --------------------------------------------------
// 2) Custom Hook: useSaveChanges
// --------------------------------------------------

/**
 * Custom hook that provides a method to save changes via API.
 * - Calls `methodApi` to make a `PUT` request.
 * - Displays success or error notifications.
 * - Ensures draft mode is properly toggled after API execution.
 *
 * @returns {Object} - Object containing `handleSaveChanges` function
 */
export const useSaveChanges = () => {
  const { toast } = createStandaloneToast();
  const router = useRouter();
  const { useApiMutation } = useBenAdmin();

  const { mutateAsync: updateChangeRequest } = useApiMutation(
    'changerequest',
    'PUT'
  );

  const { mutateAsync: publishChangeRequest } = useApiMutation(
    'publish',
    'POST'
  );

  // Use inline function with useCallback instead of the factory pattern
  const handleSaveChanges = useCallback(
    async (
      _endpoint: string,
      data: Record<string, any>,
      restParams: RestParams,
      publish = false,
      autoSave = false
    ) => {
      try {
        // Save changes with PUT request
        await updateChangeRequest({
          pathParams: { id: restParams.requestId },
          ...data,
        });

        // Publish if requested
        if (publish) {
          try {
            await publishChangeRequest({
              pathParams: { id: restParams.requestId },
            });
          } catch (error: any) {
            // Only throw if it's not a "success" error response
            if (!(error?.message?.status === 'success')) {
              throw error;
            }
          }
        }

        // Navigate and show success message based on whether it's autosave or manual save
        if (!autoSave) {
          router.push(`/ben-admin/organization/${data?.organization_id}`);

          toast({
            title: 'Success',
            description: 'Your change request has been saved successfully.',
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'bottom', // Standard toast position for manual saves
          });
        } else {
          // Discreet toast for autosave in top-right corner
          toast({
            title: 'Auto-saved',
            description: 'Changes auto-saved',
            status: 'info',
            duration: 2000,
            isClosable: true,
            position: 'top-right',
            variant: 'subtle',
          });
        }
      } catch (error: any) {
        if (!error?.response?.data?.message?.includes('Validation')) {
          toast({
            title: autoSave ? 'Auto-save Failed' : 'Save Failed',
            description:
              error?.message?.message ||
              error?.message ||
              'An error occurred while saving your change request. Please try again.',
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: autoSave ? 'top-right' : 'bottom',
          });
        }

        throw error;
      }
    },
    [updateChangeRequest, publishChangeRequest, toast, router]
  );

  return { handleSaveChanges };
};

/**
 * removeNullsDeep
 *
 * This utility function:
 *  1) Flattens a deeply nested object into dot-notation keys.
 *  2) Removes any keys whose values are null, undefined, or the string "null".
 *  3) Reconstructs (unflattens) the object to its original nested structure.
 *
 * Developer Notes:
 * - Arrays are not flattened by default; if you need to handle arrays of nested objects, you can adjust the flatten/unflatten logic.
 * - If you want to keep empty strings (""), remove the check for `"null"`.
 * - If you want to remove empty objects, you can add logic in unflattenObject to skip them if they remain empty.
 *
 * Usage Example:
 *   const prunedData = removeNullsDeep(yourNestedData);
 *   // prunedData is now the same shape as yourNestedData, but with nulls removed.
 */
export function removeNullsDeep(data: any): any {
  // 1) Flatten the nested object
  const flattened = flattenObject(data);

  // 2) Remove null keys from the flat object
  const cleanedFlat = removeNullKeys(flattened);

  // 3) Unflatten to restore the original structure
  const result = unflattenObject(cleanedFlat);

  return result;

  /**
   * flattenObject
   * Recursively flattens a nested object into a dot-notation object.
   */
  function flattenObject(
    obj: Record<string, any>,
    prefix = ''
  ): Record<string, any> {
    const out: Record<string, any> = {};
    for (const key in obj) {
      if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;

      const value = obj[key];
      const newKey = prefix ? `${prefix}.${key}` : key;

      // If it's a non-null object and not an array/Date, recurse
      if (
        value &&
        typeof value === 'object' &&
        !Array.isArray(value) &&
        !(value instanceof Date)
      ) {
        Object.assign(out, flattenObject(value, newKey));
      } else {
        out[newKey] = value;
      }
    }
    return out;
  }

  /**
   * removeNullKeys
   * Removes keys from a flat object if their values are null, undefined, or "null".
   */
  function removeNullKeys(flatObj: Record<string, any>): Record<string, any> {
    const cleaned: Record<string, any> = {};
    for (const key in flatObj) {
      if (!Object.prototype.hasOwnProperty.call(flatObj, key)) continue;
      const value = flatObj[key];

      // Skip null, undefined, or the string "null"
      if (value === null || value === undefined || value === 'null') {
        continue;
      }
      cleaned[key] = value;
    }
    return cleaned;
  }

  /**
   * unflattenObject
   * Reconstructs a nested object from a dot-notation object.
   */
  function unflattenObject(flatObj: Record<string, any>): any {
    const result: Record<string, any> = {};
    for (const flatKey in flatObj) {
      if (!Object.prototype.hasOwnProperty.call(flatObj, flatKey)) continue;

      const value = flatObj[flatKey];
      const keys = flatKey.split('.');
      let current = result;

      // Walk through each key segment
      for (let i = 0; i < keys.length; i++) {
        const segment = keys[i];
        if (i === keys.length - 1) {
          // Final segment: assign the value
          current[segment] = value;
        } else {
          // Intermediate segment: ensure an object exists
          if (!current[segment] || typeof current[segment] !== 'object') {
            current[segment] = {};
          }
          current = current[segment];
        }
      }
    }
    return result;
  }
}

export const useIsChangeRequestPage = () => {
  const pathname = usePathname();

  const isChangeRequestIntake = pathname?.includes('/change-request-intake/');

  return Boolean(isChangeRequestIntake);
};

export const useIsIntakeOverview = () => {
  const searchParams = useSearchParams();
  const isChangeRequestIntake = useIsChangeRequestPage();

  if (typeof window === 'undefined' || !searchParams) {
    return false;
  }

  const tabParam = searchParams.get('tab');
  const hasNoTab = tabParam === null;

  return isChangeRequestIntake && hasNoTab;
};

export const useShowOrganizationHeader = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  if (typeof window === 'undefined' || !searchParams) {
    return false;
  }

  const isChangeRequestIntake = pathname?.includes('/change-request-intake/');
  const containsSection = searchParams.toString().includes('section');

  return Boolean(isChangeRequestIntake && !containsSection);
};

export const onNewChange = async (
  orgId: number | null,
  toast: any,
  dateInput: string,
  router: any,
  isOpen: boolean,
  onClose: () => void,
  data: OrganizationDetails,
  createChangeRequest: any
) => {
  if (!dateInput) return;

  const cleanedData = removeNullsDeep(data);

  try {
    sessionStorage.removeItem('selectedChangeRequest');

    const newData = {
      organization_id: orgId,
      plan_id: data.plan.plan_id,
      target_effective_date: dateInput,
      status: 0,
      type: 1,
      change_content: cleanedData,
    };

    const res = await createChangeRequest({
      ...newData,
    });

    if (isOpen) {
      onClose();
    }
    router.push(
      `/ben-admin/organization/${orgId}/change-request-intake/${res.id}`
    );
  } catch (error) {
    console.error('Error creating change request:', error);
    toast({
      title: 'Error Creating Change Request',
      status: 'error',
      description:
        'There was an error creating a new Change Request. Please try again.',
      duration: 5000,
    });
  }
};

// Define the ChangeRequest interface if not already imported
interface ChangeRequest {
  change_request_id: string;
  status: string;
  target_effective_date: string;
  type: string;
  // Add other properties as needed
}

/**
 * Creates a reusable form submission handler for change requests
 * @param formMethods - React Hook Form methods
 * @param publish - Whether to publish the changes after saving
 * @returns A function to be used with formMethods.handleSubmit()
 */
export const useSaveChangeRequestHandler = (
  formMethods: UseFormReturn<any>,
  publish = false,
  autoSave = false
) => {
  // Get the save changes handler
  const { handleSaveChanges } = useSaveChanges();

  /**
   * Submit handler for change requests that:
   * 1. Retrieves the change request data from session storage
   * 2. Cleans the form data by removing null fields
   * 3. Builds and submits the payload
   * 4. Handles form reset after successful submission
   */
  const submitHandler = useCallback(
    async (data: OrganizationDetails) => {
      try {
        // Retrieve change request data from session storage
        const changeRequest: ChangeRequest | null = JSON.parse(
          sessionStorage.getItem('selectedChangeRequest') || 'null'
        );

        const requestId = changeRequest?.change_request_id || '';

        // First replace empty strings with nulls
        const processedData = replaceEmptyStringsWithNulls(data);

        // Then clean the data by removing null fields if needed
        const cleanedData = removeNullsDeep(processedData);

        // Build the payload to be saved
        const dataToSave = {
          organization_id: data?.organization?.organization_id,
          status: changeRequest?.status,
          target_effective_date: changeRequest?.target_effective_date,
          type: changeRequest?.type,
          change_content: cleanedData,
        };

        // Save changes and handle the response
        await handleSaveChanges(
          'changeRequest',
          dataToSave,
          { requestId },
          publish,
          autoSave
        );

        // Only reset form state on manual save, not on autosave
        if (!autoSave) {
          formMethods.reset();
        }
      } catch (error) {
        console.error('Error processing form data:', error);
        throw error;
      }
    },
    [formMethods, handleSaveChanges, publish, autoSave]
  );

  return submitHandler;
};

/**
 * replaceEmptyStringsWithNulls
 *
 * This utility function:
 *  1) Flattens a deeply nested object into dot-notation keys.
 *  2) Replaces any empty strings with null values.
 *  3) Reconstructs (unflattens) the object to its original nested structure.
 *
 * Developer Notes:
 * - Uses the same flattening/unflattening approach as removeNullsDeep to handle deeply nested structures
 * - Preserves arrays, Dates, and other non-plain objects
 * - Only replaces empty strings (""), all other values are preserved
 *
 * Usage Example:
 *   const processedData = replaceEmptyStringsWithNulls(yourNestedData);
 *   // processedData is now the same shape as yourNestedData, but with empty strings replaced by null.
 */
/**
 * Deeply replaces all empty strings with null values in any object structure
 * Handles arrays, nested objects, and primitive values
 *
 * @param data - The data to process (can be any type)
 * @returns The processed data with all empty strings replaced by null
 */
export function replaceEmptyStringsWithNulls(data: any): any {
  // Handle primitive values directly
  if (data === null || data === undefined) {
    return data;
  }

  if (data === '') {
    return null;
  }

  if (typeof data !== 'object') {
    return data;
  }

  // Handle arrays
  if (Array.isArray(data)) {
    return data.map((item) => replaceEmptyStringsWithNulls(item));
  }

  // Handle objects (including nested objects)
  const result: Record<string, any> = {};

  for (const key of Object.keys(data)) {
    // Process each property recursively
    result[key] = replaceEmptyStringsWithNulls(data[key]);
  }

  return result;
}
