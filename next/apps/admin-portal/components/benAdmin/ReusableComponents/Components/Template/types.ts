import { ReactNode } from 'react';

import { TemplateFieldGroup } from '../../Models/types';
import { FormSections } from '../Form/components/hooks/Completion/types';

export interface TemplateProps {
  /** Main section title displayed in the accordion header */
  title: string;
  /** Array of field groups to render within the template */
  fieldGroups: TemplateFieldGroup[];
  /** Padding applied to the accordion panel content */
  panelPadding?: string | number;
  /** Background color of the accordion item */
  backgroundColor?: string;
  /** Color of the section title text */
  titleColor?: string;
  /** Index of the default expanded accordion item */
  defaultIndex?: number;
  /** Whether the form fields should be editable */
  editable?: boolean;
  /** Whether to display in guided mode with summaries instead of form fields */
  isGuided?: boolean;
  /** Callback for when the active item/section changes */
  onUpdateActiveItem?: (section: string, tab?: string) => void;
  /** Section identifier for completion tracking */
  formSections?: FormSections;
  /** Whether to display custom component */
  customComponent?: ReactNode | null;
  /** Optional array of toggle field groups to display when toggle is active */
  toggleFieldGroups?: TemplateFieldGroup[];
  /** Optional string for toggle name to display when toggle is active */
  toggleName?: string;
  /** Optional boolean used to record the index of the object that the user wants to edit */
  recordIndex?: boolean;
  /** Optional map of dropdown options to display instead of toggle */
  dropdownMap?: Record<number, string>;

  /** Optional boolean to disable the accordion if needed */
  disableAccordion?: boolean;

  /** Optional allows you to show the Get Started button even if accordion is disabled */
  enableButton?: boolean;

  /** Optional boolean to hide validation errors for clinical-design section */
  hideClinicalDesignErrors?: boolean;
  errorCount?: number;
  /** Optional array of tab names that have validation errors */
  tabErrors?: string[];
}

export interface UiState {
  isExpanded: boolean;
  isHeaderVisible: boolean;
  isToggleActive: boolean;
  selectedDropdownValue: number;
  hoverTitle: string;
  isBodyVisible: boolean;
}
