import Template from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Template';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { ReactNode } from 'react';

import { FormSections } from '../Form/components/hooks/Completion/types';

type GenericTemplateContainerProps = {
  fieldGroups: Record<string, TemplateFieldGroup>;
  title: string;
  isGuided?: boolean;
  onUpdateActiveItem?: (section: string, tab?: string) => void;
  formSections?: FormSections;
  customComponent?: ReactNode | null;
  toggleFieldGroups?: Record<string, TemplateFieldGroup>;
  toggleName?: string;
  recordIndex?: boolean;
  dropdownMap?: Record<number, string>;
  disableAccordion?: boolean;
  enableButton?: boolean;
  hideClinicalDesignErrors?: boolean;
  errorCount?: number;
  tabErrors?: string[];
};

export const GenericTemplateContainer = ({
  fieldGroups,
  title,
  isGuided,
  onUpdateActiveItem,
  formSections,
  customComponent,
  toggleFieldGroups,
  toggleName,
  recordIndex,
  dropdownMap,
  disableAccordion,
  enableButton,
  hideClinicalDesignErrors,
  errorCount,
  tabErrors,
}: GenericTemplateContainerProps) => {
  const templateFieldGroups = Object.values(fieldGroups);
  const templateToggleFieldGroups = toggleFieldGroups
    ? Object.values(toggleFieldGroups)
    : undefined;

  return (
    <Template
      title={title}
      fieldGroups={templateFieldGroups}
      isGuided={isGuided}
      onUpdateActiveItem={onUpdateActiveItem}
      formSections={formSections}
      customComponent={customComponent}
      toggleFieldGroups={templateToggleFieldGroups}
      toggleName={toggleName}
      recordIndex={recordIndex}
      dropdownMap={dropdownMap}
      disableAccordion={disableAccordion}
      enableButton={enableButton}
      hideClinicalDesignErrors={hideClinicalDesignErrors}
      errorCount={errorCount}
      tabErrors={tabErrors}
    />
  );
};
