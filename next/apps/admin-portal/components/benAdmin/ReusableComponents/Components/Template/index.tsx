import {
  ArrowForwardIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Flex,
  Heading,
  Select,
  Switch,
  Tag,
  Text,
  useToken,
} from '@chakra-ui/react';
import React, { useCallback, useEffect, useMemo } from 'react';

import { ValidationErrorIndicator } from '../../../organization/components/ChangeRequestIntake/Validations/ValidationErrorIndicator';
import { useFormCompletionTracking } from '../Form/components/hooks/Completion/useFormCompletion';
import { useTemplateState } from './hooks/useTemplateState';
import ReusableFormLayout from './ReusableFormLayout';
import { TemplateProps } from './types';
import { formUtils } from './utils/formUtils';
import { urlUtils } from './utils/urlUtils';

// Common style constants
const BORDER_RADIUS = 'md';
const PRIMARY_COLOR = '#008a42';
const PRIMARY_HOVER_COLOR = '#007a38';
const PRIMARY_LIGHT_COLOR = 'rgba(0, 138, 66, 0.1)';

export default function Template({
  title,
  fieldGroups,
  panelPadding = 8,
  backgroundColor = 'white',
  titleColor = '#666666',
  editable,
  isGuided = false,
  onUpdateActiveItem,
  formSections,
  customComponent,
  toggleFieldGroups,
  toggleName = 'Alternate View',
  recordIndex = false,
  dropdownMap,
  disableAccordion = false,
  enableButton,
  errorCount = 0,
  tabErrors,
}: TemplateProps) {
  // Animation duration for transitions
  const [animationDurationStr] = useToken('transition.duration', ['normal']);
  const animationDuration = useMemo(
    () => parseInt(animationDurationStr),
    [animationDurationStr]
  );

  // Use our custom hook for managing template UI state
  const {
    uiState,
    updateUiState,
    accordionRef,
    accordionHeaderRef,
    accordionBodyRef,
  } = useTemplateState(title, animationDuration);

  // Essential component values
  const section = formSections?.id;
  const formMethods = fieldGroups[0]?.formMethods;

  // Get disableAccordion prop value

  // Check if dropdownMap is empty
  const hasDropdownOptions = useMemo(() => {
    return !!dropdownMap && Object.keys(dropdownMap || {}).length > 0;
  }, [dropdownMap]);

  // Feature flags
  const showToggle =
    isGuided &&
    toggleFieldGroups &&
    toggleFieldGroups.length > 0 &&
    !hasDropdownOptions;
  const showDropdown = hasDropdownOptions;

  // Form completion
  const { overallCompletion } = useFormCompletionTracking(
    formMethods,
    formSections
  );

  // Completion percentage memo
  const completionPercentage = useMemo(() => {
    if (overallCompletion !== undefined) return overallCompletion;
    if (!section || !formMethods) return 0;
    return formUtils.getValue(formMethods, `percentages.${section}`, 0);
  }, [overallCompletion, section, formMethods]);

  const isComplete = completionPercentage === 100;

  // Determine which field groups are active based on toggle/dropdown state
  const activeFieldGroups = useMemo(() => {
    if (showDropdown) {
      return fieldGroups;
    }
    return uiState.isToggleActive && showToggle
      ? toggleFieldGroups
      : fieldGroups;
  }, [
    uiState.isToggleActive,
    showToggle,
    showDropdown,
    toggleFieldGroups,
    fieldGroups,
  ]);

  // Calculate number of options
  const optionsCount = useMemo(() => {
    return hasDropdownOptions ? Object.keys(dropdownMap || {}).length : 0;
  }, [hasDropdownOptions, dropdownMap]);

  // URL Parameters Handling
  const getUrlParameters = useCallback(() => {
    return urlUtils.getParams();
  }, []);

  // Callbacks
  const toggleAccordion = useCallback(
    (isExpanded: boolean) => {
      updateUiState({ isExpanded });
    },
    [updateUiState]
  );

  const closeAccordion = useCallback(() => {
    toggleAccordion(false);
  }, [toggleAccordion]);

  const handleToggleChange = useCallback(() => {
    const newToggleState = !uiState.isToggleActive;
    updateUiState({ isToggleActive: newToggleState });
    if (section && formMethods) {
      const toggles = formUtils.getValue(formMethods, 'toggles', {});
      formUtils.setValue(formMethods, 'toggles', {
        ...toggles,
        [section]: newToggleState,
      });
    }
  }, [uiState.isToggleActive, section, formMethods, updateUiState]);

  const handleDropdownChange = useCallback(
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      const index = parseInt(event.target.value || '0', 10);
      const newTitle =
        dropdownMap && hasDropdownOptions ? dropdownMap[index] || title : title;
      requestAnimationFrame(() => {
        updateUiState({
          selectedDropdownValue: index,
          isToggleActive: index !== 0,
          hoverTitle: newTitle,
        });
        if (section && formMethods) {
          const toggles = formUtils.getValue(formMethods, 'toggles', {});
          formUtils.setValue(formMethods, 'toggles', {
            ...toggles,
            [section]: index !== 0,
            [`${section}_selectedIndex`]: index,
          });
        }
        if (index !== 0 && hasDropdownOptions) {
          urlUtils.updateParams({
            index: index.toString(),
            title: encodeURIComponent(newTitle),
          });
        } else {
          urlUtils.updateParams({
            index: null,
            title: null,
          });
        }
      });
    },
    [
      section,
      formMethods,
      hasDropdownOptions,
      dropdownMap,
      title,
      updateUiState,
    ]
  );

  const handleGetStarted = useCallback(
    (index = 0, sectionTitle?: string, edit = false) => {
      if (!onUpdateActiveItem || !section) return;
      onUpdateActiveItem(section);
      if (uiState.isToggleActive || recordIndex) {
        const titleToUse = sectionTitle || fieldGroups?.[0]?.subtitle || '';
        urlUtils.updateParams({
          index: index.toString(),
          title: encodeURIComponent(titleToUse),
          edit: String(edit),
        });
      }
    },
    [
      onUpdateActiveItem,
      section,
      uiState.isToggleActive,
      recordIndex,
      fieldGroups,
    ]
  );

  // Effects
  useEffect(() => {
    const { indexParam, titleParam } = getUrlParameters();
    let initialTitle = title;
    if (titleParam) {
      initialTitle = decodeURIComponent(titleParam);
    } else if (
      indexParam &&
      hasDropdownOptions &&
      dropdownMap &&
      dropdownMap[Number(indexParam)]
    ) {
      initialTitle = dropdownMap[Number(indexParam)];
    } else if (dropdownMap && hasDropdownOptions) {
      initialTitle = dropdownMap[0] || title;
    }
    let initialDropdownValue = 0;
    if (indexParam && !isNaN(Number(indexParam)) && hasDropdownOptions) {
      const idx = Number(indexParam);
      if (dropdownMap && dropdownMap[idx] !== undefined) {
        initialDropdownValue = idx;
      }
    }
    const initialToggleState =
      formUtils.getValue(formMethods, `toggles.${section}`, false) ||
      (showDropdown && initialDropdownValue !== 0);
    updateUiState({
      hoverTitle: initialTitle,
      selectedDropdownValue: initialDropdownValue,
      isToggleActive: initialToggleState,
    });
  }, [
    title,
    dropdownMap,
    hasDropdownOptions,
    section,
    formMethods,
    showDropdown,
    getUrlParameters,
    updateUiState,
  ]);

  useEffect(() => {
    // Only proceed with the effect logic if we have dropdown options
    if (!hasDropdownOptions) return;

    const { indexParam, titleParam } = getUrlParameters();

    if (dropdownMap && !indexParam && !titleParam) {
      const defaultIndex = '0';
      const defaultTitle = dropdownMap[0] || title;

      // Use requestAnimationFrame to defer the URL update to the next frame
      requestAnimationFrame(() => {
        urlUtils.updateParams({
          index: defaultIndex,
          title: encodeURIComponent(defaultTitle),
        });

        updateUiState({
          selectedDropdownValue: 0,
          hoverTitle: defaultTitle,
        });
      });
    }
  }, [hasDropdownOptions, dropdownMap, title, getUrlParameters, updateUiState]);

  // Compute the accordion's top position relative to the viewport.
  // Use Infinity as a fallback if the ref isn't set yet.
  const accordionRectTop = accordionRef.current
    ? accordionRef.current.getBoundingClientRect().top
    : Infinity;

  const fixedTitlePosition = 80;

  // Rendering Helpers
  const shouldShowFloating =
    uiState.isExpanded &&
    !uiState.isHeaderVisible &&
    uiState.isBodyVisible &&
    accordionRectTop < fixedTitlePosition;

  const renderFormSection = useCallback(
    (group: any, index: number) => {
      const enhancedFormMethods = formUtils.enhanceFormMethods(
        group.formMethods
      );

      return (
        <ReusableFormLayout
          key={`form-section-${index}`}
          fields={group.fields}
          columns={group.columns || 2}
          editable={editable || isGuided}
          onSubmit={group.handleSubmit}
          title={group.subtitle}
          formMethods={enhancedFormMethods}
          tab={group.tab}
          onUpdateActiveItem={onUpdateActiveItem}
          section={section}
          tabErrors={tabErrors}
        />
      );
    },
    [editable, isGuided, onUpdateActiveItem, section, tabErrors]
  );

  const floatingTitle = useMemo(() => {
    if (!shouldShowFloating) return null;
    const { titleParam } = getUrlParameters();
    const displayTitle =
      titleParam && title === 'Plan Design'
        ? decodeURIComponent(titleParam)
        : title;
    return (
      <Box
        position="fixed"
        top="80px"
        right="130px"
        zIndex={999}
        maxWidth="400px"
        display="flex"
        alignItems="center"
        data-testid="floating-title"
      >
        <Text fontSize="xl" fontWeight="bold" color={titleColor} noOfLines={1}>
          {displayTitle}
        </Text>
      </Box>
    );
  }, [shouldShowFloating, getUrlParameters, title, titleColor]);

  const quickCloseButton = useMemo(() => {
    if (!shouldShowFloating) return null;
    return (
      <Box
        position="fixed"
        top="75px"
        right="60px"
        zIndex={999}
        display="flex"
        alignItems="center"
        justifyContent="center"
        width="40px"
        height="40px"
        borderRadius="full"
        boxShadow="lg"
        cursor="pointer"
        transition="all 0.2s"
        bg={PRIMARY_COLOR}
        _hover={{ bg: PRIMARY_HOVER_COLOR }}
        onClick={closeAccordion}
        data-testid="quick-close-button"
      >
        <ChevronUpIcon color="white" boxSize={5} />
      </Box>
    );
  }, [shouldShowFloating, closeAccordion]);

  // Options count tag
  const optionsCountTag = useMemo(() => {
    if (!showDropdown || !optionsCount) return null;
    return (
      <Tag
        size="sm"
        borderRadius="full"
        variant="solid"
        bg="gray.200"
        color="gray.800"
        ml={2}
        fontWeight="medium"
        py={1}
        data-testid="options-count-tag"
      >
        {optionsCount} Options
      </Tag>
    );
  }, [showDropdown, optionsCount]);

  const completionIndicator = useMemo(() => {
    if (!isGuided || uiState.isToggleActive) return null;
    if (isComplete) {
      return (
        <Box
          px={3}
          py={1}
          borderRadius={BORDER_RADIUS}
          fontSize="sm"
          fontWeight="medium"
          bg="green.100"
          color="green.800"
          data-testid="completion-indicator-complete"
        >
          Completed
        </Box>
      );
    }
    if (completionPercentage > 0) {
      return (
        <Box
          px={3}
          py={1}
          borderRadius={BORDER_RADIUS}
          fontSize="sm"
          fontWeight="medium"
          bg="blue.50"
          color="blue.800"
          data-testid="completion-indicator-progress"
        >
          {completionPercentage}% Complete
        </Box>
      );
    }
    return null;
  }, [isGuided, uiState.isToggleActive, isComplete, completionPercentage]);

  const toggleSwitch = useMemo(() => {
    if (!showToggle) return null;
    return (
      <Flex align="center" mr={4}>
        <Text fontSize="sm" mr={2}>
          {toggleName}
        </Text>
        <Switch
          colorScheme="green"
          size="md"
          isChecked={uiState.isToggleActive}
          onChange={handleToggleChange}
          data-testid="toggle-switch"
        />
      </Flex>
    );
  }, [showToggle, uiState.isToggleActive, toggleName, handleToggleChange]);

  const planSelector = useMemo(() => {
    if (!showDropdown) return null;
    console.log('dropdownMap', dropdownMap);
    return (
      <Flex align="center" mr={4} onClick={(e) => e.stopPropagation()}>
        <Select
          icon={<ChevronDownIcon />}
          variant="outline"
          size="sm"
          borderColor={PRIMARY_COLOR}
          color="green.600"
          borderRadius={BORDER_RADIUS}
          _hover={{ bg: 'green.50', borderColor: 'green.600' }}
          _focus={{
            borderColor: PRIMARY_COLOR,
            boxShadow: 'none',
            outline: 'none',
          }}
          _active={{
            borderColor: PRIMARY_COLOR,
            boxShadow: 'none',
            outline: 'none',
          }}
          data-testid="plan-selector"
          value={uiState.selectedDropdownValue?.toString() || ''}
          onChange={handleDropdownChange}
        >
          {Object.entries(dropdownMap || {}).map(([key, value]) => (
            <option key={key} value={key}>
              {value}
            </option>
          ))}
        </Select>
      </Flex>
    );
  }, [
    showDropdown,
    uiState.selectedDropdownValue,
    handleDropdownChange,
    dropdownMap,
  ]);

  const errorIndicator = (() => {
    if (!isGuided) return null;

    // Only show errors if this specific section has errors
    const validErrorCount = typeof errorCount === 'number' ? errorCount : 0;

    if (validErrorCount === 0) return null; // No errors for this section = no indicator

    return <ValidationErrorIndicator errorCount={validErrorCount} />;
  })();

  const getStartedButton = useMemo(() => {
    if (!isGuided || !onUpdateActiveItem || !section) return null;
    return (
      <Box
        as="div"
        role="button"
        onClick={() => handleGetStarted()}
        mr={4}
        px={3}
        py={2}
        borderWidth="1px"
        borderColor={PRIMARY_COLOR}
        color={PRIMARY_COLOR}
        borderRadius={BORDER_RADIUS}
        fontSize="sm"
        fontWeight="medium"
        display="flex"
        alignItems="center"
        cursor="pointer"
        _hover={{ bg: PRIMARY_LIGHT_COLOR }}
        data-testid="get-started-button"
      >
        <Text mr={2}>Get Started</Text>
        <ArrowForwardIcon />
      </Box>
    );
  }, [isGuided, onUpdateActiveItem, section, handleGetStarted]);

  // Add this button outside the Accordion
  const standaloneGetStartedButton = useMemo(() => {
    // Only show when accordion is disabled but button should be enabled
    if (!disableAccordion || !enableButton) return null;

    // Keep the same logic for when to show the button
    if (!isGuided || !onUpdateActiveItem || !section) return null;

    return (
      <Box position="absolute" right="60px" top="16px" zIndex={1}>
        <Box
          as="div"
          role="button"
          onClick={() => handleGetStarted()}
          px={3}
          py={2}
          borderWidth="1px"
          borderColor={PRIMARY_COLOR}
          color={PRIMARY_COLOR}
          borderRadius={BORDER_RADIUS}
          fontSize="sm"
          fontWeight="medium"
          display="flex"
          alignItems="center"
          cursor="pointer"
          _hover={{ bg: PRIMARY_LIGHT_COLOR }}
          data-testid="standalone-get-started-button"
        >
          <Text mr={2}>Get Started</Text>
          <ArrowForwardIcon />
        </Box>
      </Box>
    );
  }, [
    disableAccordion,
    enableButton,
    isGuided,
    onUpdateActiveItem,
    section,
    handleGetStarted,
  ]);

  // Conditionally show the inline button
  const conditionalGetStartedButton = useMemo(() => {
    // Hide the inline button when we're showing the standalone one
    if (disableAccordion && enableButton) return null;
    return getStartedButton;
  }, [disableAccordion, enableButton, getStartedButton]);

  const renderedContent = useMemo(() => {
    if (customComponent) return customComponent;
    if (!activeFieldGroups?.length) return null;
    return activeFieldGroups.map((group, index) => {
      return renderFormSection(group, index);
    });
  }, [customComponent, activeFieldGroups, renderFormSection]);

  return (
    <Box position="relative">
      {standaloneGetStartedButton}
      <Accordion
        allowMultiple
        borderRadius={BORDER_RADIUS}
        onChange={(indices: number[]) => toggleAccordion(indices.length > 0)}
        index={uiState.isExpanded ? [0] : []}
        data-testid="template-accordion"
      >
        <AccordionItem
          ref={accordionRef}
          mb={8}
          background={backgroundColor}
          boxShadow="md"
          isDisabled={disableAccordion}
        >
          <Flex bg="gray.50" alignItems="center" ref={accordionHeaderRef}>
            <AccordionButton flex="1">
              <Flex align="center" flex="1" textAlign="left">
                <Heading fontSize={16} m={4} color={titleColor}>
                  {title}
                </Heading>
                {optionsCountTag}
                {completionIndicator}
                {errorIndicator}
              </Flex>
              {toggleSwitch}
              {planSelector}
              {conditionalGetStartedButton} {/* Use the conditional version */}
              <AccordionIcon />
            </AccordionButton>
          </Flex>
          <AccordionPanel pl={panelPadding} ref={accordionBodyRef}>
            {renderedContent}
          </AccordionPanel>
        </AccordionItem>
      </Accordion>
      {floatingTitle}
      {quickCloseButton}
    </Box>
  );
}
