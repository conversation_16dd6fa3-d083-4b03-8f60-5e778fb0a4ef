/**
 * AccordionView component for DynamicCollectionSection
 */
import {
  AddIcon,
  ArrowForwardIcon,
  ChevronDownIcon,
  EditIcon,
} from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Flex,
  HStack,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Portal,
} from '@chakra-ui/react';
import React, { useCallback, useMemo } from 'react';

import { AccordionViewProps } from '../../types/types';
import AccordionDetails from './AccordionDetails';
import AccordionSummary from './AccordionSummary';

/**
 * Renders a collection of items in accordion format
 */
const AccordionView: React.FC<AccordionViewProps> = ({
  items,
  basePath,
  formMethods,
  openAccordions,
  toggleAccordion,
  onEdit,
  onRemove,
  onGetStarted,
  hideAccordionIcon,
  editButtonText,
  optionsButtonText,
  additionalOptions,
  deleteMenuItemText,
  accordionSummaryComponent: CustomAccordionSummary,
  accordionDetailsComponent: CustomAccordionDetails,
  tableComponent,
  itemLabelKey,
  distinct,
  showOptionsButton,
  addChannelButtonText,
  onAddChannel,
  getStatusIcon,
  getPlanDesignValidationStatus,
}) => {
  // Use provided components or fall back to defaults
  const RenderAccordionSummary = CustomAccordionSummary || AccordionSummary;
  const RenderAccordionDetails = CustomAccordionDetails || AccordionDetails;

  // Filter and process items based on distinct settings
  const processedItems = useMemo(() => {
    let processedItems = [...items];

    if (distinct) {
      // First apply the type filter if provided
      if (distinct.filter) {
        processedItems = processedItems.filter(distinct.filter);
      }

      // Then apply distinct logic only to the filtered items
      if (distinct.isDistinct && distinct.indicator) {
        const seen = new Set();
        processedItems = processedItems.filter((item) => {
          if (!item || !item[distinct.indicator]) return false;

          // Create a composite key using only the tier_ind
          const value = item[distinct.indicator];

          if (seen.has(value)) {
            return false;
          }
          seen.add(value);
          return true;
        });
      }
    }

    return processedItems;
  }, [items, distinct]);

  // Helper function to determine if options button should be shown
  const shouldShowOptions = useCallback(
    (item: any) => {
      if (typeof showOptionsButton === 'boolean') {
        return showOptionsButton;
      }
      if (typeof showOptionsButton === 'function') {
        return showOptionsButton(item);
      }
      return false;
    },
    [showOptionsButton]
  );

  return (
    <Accordion allowMultiple>
      {processedItems.map((item, index: number) => {
        const isOpen = !!openAccordions[index];
        const showOptions = shouldShowOptions(item);
        return (
          <AccordionItem
            key={`${basePath}-accordion-${index}`}
            mb={4}
            borderRadius="lg"
            boxShadow="0px 1px 3px rgba(0, 0, 0, 0.1)"
            border="none"
            bg="white"
            overflow="hidden"
          >
            {/* Wrapper that contains both the toggle area and the buttons */}
            <Box
              as="div"
              bg="gray.50"
              _hover={{ bg: 'gray.100' }}
              position="relative"
            >
              <Flex justifyContent="space-between" alignItems="center">
                {/* Content part that toggles the accordion */}
                <Box
                  as={AccordionButton}
                  py={6}
                  px={4}
                  _hover={{ bg: 'transparent' }}
                  flex="1"
                  textAlign="left"
                  onClick={() => toggleAccordion(index)}
                >
                  <RenderAccordionSummary
                    formMethods={formMethods}
                    basePath={basePath}
                    index={index}
                    isOpen={isOpen}
                    toggleAccordion={() => toggleAccordion(index)}
                    onRemove={() => onRemove(index)}
                    onEdit={() => onEdit(index)}
                    hideButtons={true}
                    editButtonText={editButtonText}
                    optionsButtonText={optionsButtonText}
                    deleteMenuItemText={deleteMenuItemText}
                    itemLabelKey={itemLabelKey}
                    items={processedItems}
                    item={item}
                    getStatusIcon={getStatusIcon}
                    getPlanDesignValidationStatus={
                      getPlanDesignValidationStatus
                    }
                  />
                </Box>

                {/* Action buttons - outside the AccordionButton but inside the hover area */}
                <HStack
                  spacing={2}
                  pr={4}
                  position="relative"
                  zIndex="10"
                  onClick={(e) => e.stopPropagation()}
                >
                  {onGetStarted && !showOptions ? (
                    // Show Get Started button if onGetStarted is provided and options shouldn't be shown
                    <Button
                      rightIcon={<ArrowForwardIcon />}
                      variant="outline"
                      size="sm"
                      colorScheme="green"
                      borderRadius="md"
                      onClick={() => onGetStarted(item, index)}
                    >
                      Get Started
                    </Button>
                  ) : showOptions ? (
                    // Show only Options button when showOptions is true
                    <Menu
                      placement="bottom-end"
                      closeOnSelect={true}
                      gutter={0}
                    >
                      <MenuButton
                        as={Button}
                        variant="outline"
                        colorScheme="green"
                        size="sm"
                        rightIcon={<ChevronDownIcon />}
                        borderRadius="md"
                      >
                        {optionsButtonText}
                      </MenuButton>
                      <Portal>
                        <MenuList zIndex={9999}>
                          {additionalOptions?.map((option: any) => (
                            <MenuItem
                              key={option.optionName}
                              onClick={() => option.onOptionClick(index)}
                              isDisabled={option.isOptionDisabled}
                            >
                              {option.optionName}
                            </MenuItem>
                          ))}
                          <MenuItem onClick={() => onRemove(index)}>
                            {deleteMenuItemText}
                          </MenuItem>
                        </MenuList>
                      </Portal>
                    </Menu>
                  ) : (
                    // Show both Edit and Options buttons for other cases
                    <>
                      <Button
                        leftIcon={<EditIcon />}
                        variant="outline"
                        size="sm"
                        colorScheme="green"
                        borderRadius="md"
                        onClick={() => onEdit(index)}
                      >
                        {editButtonText}
                      </Button>
                      <Menu
                        placement="bottom-end"
                        closeOnSelect={true}
                        gutter={0}
                      >
                        <MenuButton
                          as={Button}
                          variant="outline"
                          colorScheme="green"
                          size="sm"
                          rightIcon={<ChevronDownIcon />}
                          borderRadius="md"
                        >
                          {optionsButtonText}
                        </MenuButton>
                        <Portal>
                          <MenuList zIndex={9999}>
                            {additionalOptions?.map((option: any) => (
                              <MenuItem
                                key={option.optionName}
                                onClick={() => option.onOptionClick(index)}
                                isDisabled={option.isOptionDisabled}
                              >
                                {option.optionName}
                              </MenuItem>
                            ))}
                            <MenuItem onClick={() => onRemove(index)}>
                              {deleteMenuItemText}
                            </MenuItem>
                          </MenuList>
                        </Portal>
                      </Menu>
                    </>
                  )}
                </HStack>
              </Flex>
            </Box>

            <AccordionPanel p={6} bg="white">
              {tableComponent ? (
                <Box mt={4}>
                  {tableComponent &&
                    showOptions &&
                    addChannelButtonText &&
                    onAddChannel && (
                      <Flex justifyContent="flex-end" mb={4}>
                        <Button
                          leftIcon={<AddIcon />}
                          variant="outline"
                          size="sm"
                          colorScheme="green"
                          borderRadius="md"
                          onClick={() => onAddChannel(item)}
                        >
                          {addChannelButtonText}
                        </Button>
                      </Flex>
                    )}
                  {typeof tableComponent === 'function'
                    ? tableComponent(item)
                    : tableComponent}
                </Box>
              ) : (
                <RenderAccordionDetails
                  formMethods={formMethods}
                  basePath={basePath}
                  index={index}
                />
              )}
            </AccordionPanel>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
};

export default AccordionView;
