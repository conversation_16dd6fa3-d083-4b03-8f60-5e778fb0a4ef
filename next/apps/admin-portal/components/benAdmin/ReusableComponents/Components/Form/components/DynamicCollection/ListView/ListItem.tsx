/**
 * ListItem component for DynamicCollectionSection
 */
import { ChevronDownIcon, EditIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  HStack,
  Link,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Portal,
  Text,
} from '@chakra-ui/react';
import get from 'lodash/get';
import React, { useMemo } from 'react';

import { calculateCompletionPercentage } from '../../hooks/DynamicCollections/useItemCompletion';
import { ItemComponentProps } from '../../types/types';

/**
 * Renders a single item in list view format
 */
const ListItem: React.FC<ItemComponentProps> = ({
  formMethods,
  basePath,
  index,
  onRemove,
  onEdit,
  editButtonText,
  optionsButtonText,
  additionalOptions,
  deleteMenuItemText,
  items,
  customNameField,
  item: propItem,
}) => {
  // Get current item using useMemo to maintain reference stability
  const currentItem = useMemo(() => {
    return propItem || items[index] || {};
  }, [items, index, propItem]);

  // Use custom name field, item name, or generate a default name based on index
  const name = useMemo(() => {
    // First try to get the custom name field value
    if (customNameField) {
      const customName = get(currentItem, customNameField);
      // Only return if we have a non-empty string value
      if (
        customName &&
        typeof customName === 'string' &&
        customName.trim() !== ''
      ) {
        return customName;
      }
    }

    // Fall back to name property if it exists and is non-empty
    if (
      currentItem.name &&
      typeof currentItem.name === 'string' &&
      currentItem.name.trim() !== ''
    ) {
      return currentItem.name;
    }

    // Last resort: use default naming
    return `Item ${index + 1}`;
  }, [currentItem, index, customNameField]);

  // Get lastUpdated info or use defaults
  const lastUpdated = useMemo(() => {
    return currentItem.lastUpdated || { by: 'User', at: '10:00am' };
  }, [currentItem]);

  // Calculate completion percentage with stable item reference
  const { completionPercentage } = useMemo(
    () => calculateCompletionPercentage(currentItem),
    [currentItem]
  );

  return (
    <Box py={4}>
      <Flex justify="space-between" align="center" mb={1}>
        <Flex align="center">
          <Link color="#137ea4" fontWeight="bold" onClick={onEdit}>
            {name}
          </Link>
          <Text
            ml={2}
            color={completionPercentage === 100 ? 'green.700' : 'blue.700'}
            fontSize="sm"
          >
            • {completionPercentage}% Complete
          </Text>
        </Flex>
        <HStack pt={5} spacing={2}>
          <Button
            leftIcon={<EditIcon />}
            variant="outline"
            size="sm"
            colorScheme="green"
            borderRadius="md"
            onClick={onEdit}
          >
            {editButtonText || 'Edit'}
          </Button>
          <Menu>
            <MenuButton
              as={Button}
              variant="outline"
              colorScheme="green"
              size="sm"
              rightIcon={<ChevronDownIcon />}
              borderRadius="md"
            >
              {optionsButtonText || 'Options'}
            </MenuButton>
            <Portal>
              <MenuList>
                {additionalOptions &&
                  additionalOptions.map((option) => (
                    <MenuItem
                      key={option.optionName}
                      onClick={option.onOptionClick}
                      isDisabled={option.isOptionDisabled}
                    >
                      {option.optionName}
                    </MenuItem>
                  ))}
                <MenuItem onClick={onRemove}>
                  {deleteMenuItemText || 'Delete'}
                </MenuItem>
              </MenuList>
            </Portal>
          </Menu>
        </HStack>
      </Flex>
      <Text color="gray.600" fontSize="sm">
        Last Updated by {lastUpdated.by}, at {lastUpdated.at}
      </Text>
    </Box>
  );
};

export default ListItem;
