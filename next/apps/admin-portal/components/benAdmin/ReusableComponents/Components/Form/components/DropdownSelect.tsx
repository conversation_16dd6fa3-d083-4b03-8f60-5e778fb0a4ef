import { ChevronDownIcon } from '@chakra-ui/icons';
import {
  Box,
  FormControl,
  FormLabel,
  Icon,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  List,
  ListItem,
  Spinner,
  useOutsideClick,
  VStack,
} from '@chakra-ui/react';
import React, { useEffect, useRef, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { FaTimes } from 'react-icons/fa';

import { FieldError, FieldWarning } from '../../InlineFieldIcons';
import { AncillaryToggleAccordion } from './AncillaryToggleAccordion';
import { CircularQuestionIcon } from './sharables/CircularQuestionIcon';
import ToggleWithAccordion from './ToggleWithAccordion';

interface EnhancedDropdownSelectProps {
  name: string;
  label?: string;
  optionsMap: { [key: string]: string };
  isOptionsMapLoading?: boolean;
  placeholder?: string;
  isRequired?: boolean;
  infoText?: string;
  allowSearch?: boolean;
  value: string;
  error?: boolean;
  errorType?: string;
  onChange: (name: string, key: string | null) => void;
  onBlur?: () => void;
  isDisabled?: boolean;
  showAncillaryToggle?: boolean;
  showToggle?: boolean;
  toggleLabel?: string;
  toggleName?: string;
  toggleValue?: boolean;
  onToggleChange?: (isChecked: boolean) => void;
  formMethods: UseFormReturn<any>;
  formPath?: string;
  sync?: boolean;
  idMap?: {
    key: string;
    value: number;
  } | null;
  syncFunction?: (
    sync: boolean,
    toggleValue: boolean,
    name: string,
    value: string | number,
    formMethods: UseFormReturn<any>
  ) => void;
  sortOptionsByLabel?: boolean;
}

const DropdownSelect: React.FC<EnhancedDropdownSelectProps> = ({
  name,
  label,
  optionsMap = {},
  isOptionsMapLoading = false,
  placeholder = 'Select an option...',
  isRequired = false,
  infoText,
  allowSearch = false,
  value,
  error,
  errorType,
  onChange,
  onBlur,
  isDisabled = false,
  showAncillaryToggle = false,
  showToggle = false,
  toggleLabel = 'Differs from Plan Design',
  toggleName,
  toggleValue = false,
  onToggleChange,
  formMethods,
  formPath,
  sync = false,
  idMap,
  syncFunction,
  sortOptionsByLabel = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [menuPosition, setMenuPosition] = useState({
    left: 0,
    top: 0,
    width: 0,
  });

  useEffect(() => {
    if (isOpen && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      setMenuPosition({
        left: rect.left,
        top: rect.bottom + 4,
        width: rect.width,
      });
    }
  }, [isOpen]);

  const isEffectivelyDisabled = isDisabled || toggleValue;

  useOutsideClick({
    ref: dropdownRef,
    handler: () => {
      setIsOpen(false);
      if (onBlur) onBlur();
    },
  });

  const getFilteredOptions = () => {
    if (allowSearch) {
      const filteredOptions = Object.entries(optionsMap).filter(([_, label]) =>
        searchValue
          ? label.toLowerCase().includes(searchValue.toLowerCase())
          : true
      );
      // use a Map here if we want to sort options alphabetically
      if (sortOptionsByLabel) {
        const sortedEntries = filteredOptions.sort((a, b) => {
          return a[1].localeCompare(b[1]);
        });
        return [...new Map(sortedEntries)];
      } else {
        return filteredOptions;
      }
    } else return [['emptyOption', placeholder], ...Object.entries(optionsMap)];
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (isEffectivelyDisabled) return;
    setSearchValue(event.target.value);
    setIsOpen(true);
  };

  const handleOptionSelect = (key: string) => {
    if (isEffectivelyDisabled) return;

    // Update the original dropdown value
    onChange(name, key === 'emptyOption' ? null : key);

    if (syncFunction) {
      syncFunction(sync, toggleValue, name, key, formMethods);
    }

    setIsOpen(false);
    setSearchValue('');
    if (onBlur) onBlur();
  };

  const handleClearInput = (event: React.MouseEvent) => {
    if (isEffectivelyDisabled) return;
    event.stopPropagation();
    onChange(name, '');
    setSearchValue('');
    setIsOpen(true);
    inputRef.current?.focus();
  };

  const handleInputClick = () => {
    if (isEffectivelyDisabled) return;
    inputRef.current?.focus();
    setIsOpen(!isOpen);
  };

  const handleToggleChange = (isChecked: boolean) => {
    if (onToggleChange) {
      onToggleChange(isChecked);

      // If toggle is turned on, clear the dropdown selection
      if (isChecked) {
        onChange(name, '');
      }
    }
  };

  return (
    <FormControl isRequired={isRequired}>
      <VStack spacing={2} align="stretch" width="100%">
        <Box>
          <Box display="flex" alignItems="flex-start" mb={1}>
            {label && (
              <FormLabel color="#676767" mb={0}>
                {label}
              </FormLabel>
            )}
            {infoText && (
              <CircularQuestionIcon tooltiptext={infoText} ml={-2} />
            )}
          </Box>
          <Box position="relative" ref={dropdownRef}>
            <InputGroup>
              {error && (
                <InputLeftElement
                  h="100%"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  pointerEvents="none"
                >
                  {(errorType === 'zodValidation' ||
                    errorType === 'required' ||
                    errorType === 'field-error') &&
                    FieldError()}
                  {errorType === 'field-warning' && FieldWarning()}
                </InputLeftElement>
              )}

              <Input
                pl={error ? '32px' : ''}
                ref={inputRef}
                value={
                  allowSearch && searchValue
                    ? searchValue
                    : optionsMap[value] || ''
                }
                placeholder={placeholder}
                onChange={allowSearch ? handleInputChange : undefined}
                readOnly={!allowSearch}
                cursor={
                  !allowSearch && !isEffectivelyDisabled
                    ? 'pointer'
                    : isEffectivelyDisabled
                    ? 'not-allowed'
                    : 'text'
                }
                onClick={handleInputClick}
                autoComplete="off"
                isDisabled={isEffectivelyDisabled}
                isInvalid={error}
                borderColor={error ? 'orange.400' : 'gray.300'}
                onBlur={() => {
                  setTimeout(() => {
                    if (!isOpen && onBlur) onBlur();
                  }, 200);
                }}
              />
              <InputRightElement
                display="flex"
                alignItems="center"
                h="100%"
                userSelect="none"
              >
                {allowSearch && searchValue ? (
                  <Icon
                    as={FaTimes}
                    cursor={isEffectivelyDisabled ? 'not-allowed' : 'pointer'}
                    color="gray.500"
                    onClick={handleClearInput}
                  />
                ) : (
                  <Icon
                    as={ChevronDownIcon}
                    cursor={isEffectivelyDisabled ? 'not-allowed' : 'pointer'}
                    color="gray.500"
                    onClick={handleInputClick}
                  />
                )}
              </InputRightElement>
            </InputGroup>
            {isOpen && !isEffectivelyDisabled && (
              <Box
                position="fixed"
                left={`${menuPosition.left}px`}
                top={`${menuPosition.top}px`}
                width={`${menuPosition.width}px`}
                boxShadow="md"
                borderWidth="1px"
                borderRadius="md"
                bg="white"
                zIndex={9999}
                maxHeight="300px"
                overflowY="auto"
              >
                <List spacing={0}>
                  {getFilteredOptions().length > 0 ? (
                    getFilteredOptions().map(([key, label]) => (
                      <ListItem
                        key={key}
                        className="dropdown-item"
                        p={2}
                        cursor="pointer"
                        _hover={{ backgroundColor: 'gray.100' }}
                        onMouseDown={() => handleOptionSelect(key)}
                      >
                        {label}
                      </ListItem>
                    ))
                  ) : (
                    <Box p={2} textAlign="center" color="gray.500">
                      {isOptionsMapLoading ? <Spinner /> : 'No options found'}
                    </Box>
                  )}
                </List>
              </Box>
            )}
          </Box>
        </Box>

        {/* Render toggle with accordion if showToggle is true */}
        {/* This was specifically created for Ben Admin, rare use case. */}

        {showAncillaryToggle && (
          <AncillaryToggleAccordion
            label={toggleLabel}
            isChecked={toggleValue}
            onChange={handleToggleChange}
            name={toggleName}
            optionsMap={optionsMap}
            formMethods={formMethods}
            formPath={formPath}
          />
        )}

        {showToggle && (
          <ToggleWithAccordion
            label={toggleLabel}
            isChecked={toggleValue}
            onChange={handleToggleChange}
            name={toggleName}
            optionsMap={optionsMap}
            formMethods={formMethods}
            formPath={formPath}
            idMap={idMap}
          />
        )}
      </VStack>
    </FormControl>
  );
};

export default DropdownSelect;
