/**
 * ListView component for DynamicCollectionSection
 */
import { Box, Flex } from '@chakra-ui/react';
import React from 'react';

import { ListViewProps } from '../../types/types';
import ListItem from './ListItem';

/**
 * Renders a collection of items in list format
 */
const ListView: React.FC<ListViewProps> = ({
  basePath,
  formMethods,
  onEdit,
  onRemove,
  editButtonText,
  optionsButtonText,
  additionalOptions,
  deleteMenuItemText,
  itemComponent: CustomListItem,
  items,
  customNameField,
}) => {
  // Use provided component or fall back to default
  const RenderListItem = CustomListItem || ListItem;

  return (
    <Box>
      {items.map((item: any, index: number) => (
        <Box
          key={`${basePath}-list-${index}`}
          mb={4}
          p={4}
          borderRadius="lg"
          boxShadow="0px 1px 3px rgba(0, 0, 0, 0.1)"
          bg="white"
        >
          <Flex justifyContent="space-between" alignItems="center">
            <Box flex="1">
              <RenderListItem
                formMethods={formMethods}
                basePath={basePath}
                index={index}
                onRemove={() => onRemove(index)}
                onEdit={() => onEdit(index)}
                editButtonText={editButtonText}
                optionsButtonText={optionsButtonText}
                additionalOptions={additionalOptions}
                deleteMenuItemText={deleteMenuItemText}
                item={item}
                items={items}
                customNameField={customNameField}
              />
            </Box>
          </Flex>
        </Box>
      ))}
    </Box>
  );
};

export default ListView;
