import { Button, Flex, Text } from '@chakra-ui/react';
import { UserInfo } from '@next/admin/components';
import { Access } from '@next/admin/components';
import { Permissions } from '@next/admin/components';
import { Roles } from '@next/admin/constants';
import { useApi } from '@next/shared/api';
import { PermissionRoleContext } from '@next/shared/contexts';
import { useCustomToast } from '@next/shared/hooks';
import { Block } from '@next/shared/ui';
import SecuredAccess from 'libs/client/admin-portal/components/src/lib/CreateAndEditUser/SecuredAccess';
import { useRouter } from 'next/navigation';
import { useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export type secureOrgs = {
  benefitGroups: [];
  brokerFirmId: string;
  brokerOrgId: string;
  brokerParentId: string;
  name: string;
  organizationNo: number;
  restrictBgGroups: boolean;
  selected: boolean;
}[];

type userInfo = {
  firstName: string;
  isActive: boolean;
  lastName: string;
  role: 'RxB Employee' | 'Broker' | 'Client' | null;
  rxbAdmin: boolean;
  username: string;
  psaAccessLevel: number | null;
};

type permissions = {
  permissionId: number;
  permissionsGroupId: number;
  slug: string;
}[];
type orgs = {
  brokerFirmId: string;
  brokerOrgId: string;
  brokerParentId: string;
  isActive: boolean;
  name: string;
  organizationNo: number;
}[];
type sendData = {
  apManageUserData: {
    apManageUserOrgs: orgs;
    apManageUserProfile: userInfo;
  };
  apManageUserPerms: permissions;
  secureOrgAccess: secureOrgs;
};

export default function CreateUser() {
  const { getUserRoles, getUserByRole, getApi, methodApi } = useApi([
    'getUserRoles',
  ]);
  const { canViewWithPerm } = useContext(PermissionRoleContext);
  const [tempPerms, setTempPerms] = useState<any[]>();
  const showToast = useCustomToast();
  const router = useRouter();
  const [role, setRole] = useState('');
  const [userInfo, setUserInfo] = useState<userInfo>({
    firstName: '',
    isActive: true,
    lastName: '',
    role: null,
    rxbAdmin: false,
    username: '',
    psaAccessLevel: 0,
  });

  const isPermToManageSecureAccess = canViewWithPerm('manageSecureOrgs');
  const [userOrgs, setUserOrgs] = useState<orgs>(
    getUserByRole?.apManageUserData?.apManageUserOrgs || []
  );
  const [userSecuredOrgs, setUserSecuredOrgs] = useState<secureOrgs>(
    getUserByRole?.secureAccessOrgs || []
  );

  const [secureOrgs, setSecureOrgs] = useState<secureOrgs>(
    getUserByRole?.secureOrgAccess || []
  );

  const [userAccessLevel, setUserAccessLevel] = useState<number | null>(
    userInfo.psaAccessLevel
  );
  const [unselectOrgs, setUnselectOrgs] = useState<any[]>([]);

  const [userPermissions, setUserPermissions] = useState<permissions>([]);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      firstName: userInfo?.firstName,
      lastName: userInfo?.lastName,
      username: userInfo?.username,
    },
    values: {
      firstName: '',
      lastName: '',
      username: '',
    },
  });

  useEffect(() => {
    if (!userInfo?.role) return;
    if (role === userInfo?.role) return;
    setUserOrgs([]);
    getApi(
      'getUserByRole',
      { role: Roles[userInfo.role] },
      {
        onSuccess: (data) => {
          setTempPerms(undefined);
          setUserOrgs(data?.apManageUserData?.apManageUserOrgs);
          setUnselectOrgs(data?.apManageUserData?.apManageUserOrgs);
        },
      }
    );
    setRole(userInfo.role);
  }, [userInfo.role, getApi, role]);

  useEffect(() => {
    setTempPerms(getUserByRole?.apManageUserPerms);
    setSecureOrgs(getUserByRole?.secureOrgAccess);
  }, [tempPerms, getUserByRole, userInfo, userOrgs]);

  const submitUser = (infoData: any) => {
    const data: sendData = {
      apManageUserData: {
        apManageUserOrgs: userOrgs,
        apManageUserProfile: {
          ...userInfo,
          ...infoData,
          psaAccessLevel: userAccessLevel,
        },
      },
      apManageUserPerms: userPermissions,
      secureOrgAccess: userSecuredOrgs,
    };
    methodApi('createUser', {
      body: { ...data },
      onSuccess: (data) => {
        if (data.status && data.status !== 200) {
          return showToast({
            title: data.detail,
            status: 'error',
            position: 'top',
          });
        }
        router.push('/admin-tools/user-management');
      },
      onError: (e) => {
        showToast({
          title: e.detail,
          status: 'error',
          position: 'top',
        });
      },
    });
  };

  return (
    <Flex borderTopRadius={'12px'} flexDirection={'column'} gap={'30px'}>
      <Flex justifyContent={'space-between'} alignItems={'center'}>
        <Text fontWeight={700} fontSize={'lg'}>
          Create User
        </Text>
        <Flex>
          <Button
            onClick={router.back}
            backgroundColor="blue.600"
            color="white"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(submitUser)}
            marginLeft="10px"
            backgroundColor="blue.600"
            color="white"
          >
            Save
          </Button>
        </Flex>
      </Flex>
      <Block title={'User Information'} subTitle={'last updated'}>
        <UserInfo
          register={register}
          setUserInfo={setUserInfo}
          roles={getUserRoles}
          errors={errors}
          user={userInfo}
          setUserOrgs={setUserOrgs}
        />
      </Block>
      <Access
        setUserOrgs={setUserOrgs}
        role={role}
        orgs={userOrgs}
        brokerData={{
          parents: getUserByRole?.brokerParents,
          firms: getUserByRole?.brokerFirms,
        }}
        unselectOrgs={unselectOrgs}
        setUserAccessLevel={setUserAccessLevel}
      />
      {isPermToManageSecureAccess && (
        <SecuredAccess
          setUserSecuredOrgs={setUserSecuredOrgs}
          secureAccessOrgs={secureOrgs}
          role={role}
        />
      )}
      <Permissions
        setUserPermissions={setUserPermissions}
        userPermissions={userPermissions}
        perms={tempPerms || []}
      />
    </Flex>
  );
}
