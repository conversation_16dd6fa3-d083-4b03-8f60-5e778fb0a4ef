import { <PERSON>, <PERSON><PERSON>, <PERSON>lex, <PERSON><PERSON>, Spinner, Text } from '@chakra-ui/react';
import { UserInfo, UserStatusTag } from '@next/admin/components';
import { Access } from '@next/admin/components';
import { Permissions } from '@next/admin/components';
import { Roles, theme } from '@next/admin/constants';
import { useApi } from '@next/shared/api';
import { PermissionRoleContext } from '@next/shared/contexts';
import { useCustomToast } from '@next/shared/hooks';
import { Block } from '@next/shared/ui';
import SecuredAccess from 'libs/client/admin-portal/components/src/lib/CreateAndEditUser/SecuredAccess';
import { useRouter, useSearchParams } from 'next/navigation';
import { useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { BsFillArrowLeftSquareFill } from 'react-icons/bs';

import { secureOrgs } from './createUser';

type userInfo = {
  firstName: string;
  isActive: boolean;
  lastName: string;
  role: 'RxB Employee' | 'Broker' | 'Client' | null;
  rxbAdmin: boolean;
  username: string;
  psaAccessLevel: number | null;
};

type permissions = {
  permissionId: number;
  permissionsGroupId: number;
  slug: string;
}[];
type orgs = {
  brokerFirmId: string;
  brokerOrgId: string;
  brokerParentId: string;
  isActive: boolean;
  name: string;
  organizationNo: number;
  selected: boolean;
}[];
type sendData = {
  apManageUserData: {
    apManageUserOrgs: orgs;
    apManageUserProfile: userInfo;
  };
  apManageUserPerms: permissions;
  secureOrgAccess: secureOrgs;
};

type brokers = {
  accountName: string;
  brokerFirmId: string;
  brokerParentId: string;
}[];

export default function CreateUser() {
  const { getUserRoles, getEditUser, getApi, methodApi } = useApi([
    'getUserRoles',
  ]);
  const { canViewWithPerm } = useContext(PermissionRoleContext);
  const showToast = useCustomToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [role, setRole] = useState('');
  const [isDisabled, setIsDisabled] = useState(true);
  const [tempPerms, setTempPerms] = useState<any[]>();
  const [currentData, setCurrentData] = useState<any>();
  const [useDefault, setUseDefault] = useState(true);
  const [loading, setLoading] = useState(true);
  const [unselectOrgs, setUnselectOrgs] = useState<any[]>([]);
  const [userInfo, setUserInfo] = useState<userInfo>({
    firstName: '',
    isActive: true,
    lastName: '',
    role: null,
    rxbAdmin: false,
    username: '',
    psaAccessLevel: null,
  });
  const [userOrgs, setUserOrgs] = useState<orgs>(
    getEditUser?.apManageUserData?.apManageUserOrgs || []
  );
  const [secureAccessOrgs, setSecureAccessOrgs] = useState<secureOrgs>(
    getEditUser?.secureOrgAccess || []
  );
  const [userSecuredOrgs, setUserSecuredOrgs] = useState<secureOrgs>();
  const [userAccessLevel, setUserAccessLevel] = useState<number | null>(
    userInfo.psaAccessLevel
  );
  const [selectedOrgs, setSelectedOrgs] = useState<orgs>([]);
  const [selectedBrokers, setSelectedBrokers] = useState<brokers>([]);

  useEffect(() => {
    if (!userInfo?.role) return;
    if (
      userInfo?.role ===
      getEditUser?.apManageUserData?.apManageUserProfile?.role
    ) {
      return;
    } else {
      setSelectedOrgs([]);
      setSelectedBrokers([]);
    }
    getApi(
      'getUserByRole',
      { role: Roles[userInfo.role] },
      {
        onSuccess: (data) => {
          setUnselectOrgs(data?.apManageUserData?.apManageUserOrgs);
          if (!data?.apManageUserData?.apManageUserProfile?.role) return;
          setTempPerms(undefined);
          setTempPerms(data?.apManageUserPerms);
          setCurrentData(data);
          setUseDefault(false);
          setSecureAccessOrgs(data?.secureOrgAccess);
        },
        onError: (e) => {
          showToast({
            title: e.detail,
            position: 'top',
            status: 'error',
            duration: 3000,
          });
        },
      }
    );
    setRole(userInfo.role);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    userInfo.role,
    getApi,
    getEditUser?.apManageUserData?.apManageUserProfile?.role,
    role,
  ]);

  useEffect(() => {
    getApi(
      'getEditUser',
      {
        userNo: searchParams?.get('userNo') as string,
      },
      {
        onSuccess: (data) => {
          setTempPerms(data?.apManageUserPerms);
        },
        onError: (e) => {
          showToast({
            title: e.detail,
            position: 'top',
            status: 'error',
            duration: 3000,
          });
        },
      }
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getApi, searchParams]);

  useEffect(() => {
    if (getEditUser) setLoading(false);
    if (
      userInfo.role &&
      userInfo.role === getEditUser?.apManageUserData?.apManageUserProfile.role
    ) {
      setCurrentData(getEditUser);
    }
  }, [getEditUser, userInfo, userInfo.role]);

  useEffect(() => {
    setUserOrgs(getEditUser?.apManageUserData?.apManageUserOrgs);
    setSecureAccessOrgs(getEditUser?.secureOrgAccess);
  }, [
    getEditUser?.apManageUserData?.apManageUserOrgs,
    getEditUser?.secureOrgAccess,
  ]);

  const isPermToManageSecureAccess = canViewWithPerm('manageSecureOrgs');

  const [userPermissions, setUserPermissions] = useState<permissions>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      firstName: userInfo?.firstName,
      lastName: userInfo?.lastName,
      username: userInfo?.username,
    },
    values: {
      firstName: '',
      lastName: '',
      username: '',
    },
  });

  useEffect(() => {
    const profile = getEditUser?.apManageUserData?.apManageUserProfile;
    if (profile?.role) setRole(profile?.role);
    if (profile) {
      setUserInfo(profile);
      setValue('firstName', profile.firstName, { shouldValidate: true });
      setValue('lastName', profile.lastName, { shouldValidate: true });
      setValue('username', profile.username, { shouldValidate: true });
    }
  }, [getEditUser?.apManageUserData?.apManageUserProfile, setValue]);

  const submitUser = (infoData: any) => {
    const getSelectedOrgs = userOrgs.filter((e) => e?.selected);
    const data: sendData = {
      apManageUserData: {
        apManageUserOrgs: getSelectedOrgs,
        apManageUserProfile: {
          ...userInfo,
          ...infoData,
          psaAccessLevel: userAccessLevel,
        },
      },
      apManageUserPerms: userPermissions,
      secureOrgAccess: userSecuredOrgs || [],
    };
    methodApi('postEditUser', {
      body: {
        ...data,
      },
      restParams: { userNo: searchParams?.get('userNo') as string },
      onSuccess: (data) => {
        if (data.status && data.status !== 200) {
          return showToast({
            title: data.detail,
            status: 'error',
            position: 'top',
            duration: 3000,
          });
        }
        router.push('/admin-tools/user-management');
      },
      onError: (e) => {
        showToast({
          title: e.detail,
          position: 'top',
          status: 'error',
        });
      },
    });
  };

  useEffect(() => {
    const selectedOrgs =
      currentData?.apManageUserData?.apManageUserOrgs?.filter(
        (org: any) => org.selected
      ) || [];
    setSelectedOrgs(selectedOrgs);

    // Combine brokerFirms from currentData and brokerParents' brokerFirms
    const allBrokerFirms = [
      ...(currentData?.brokerFirms || []),
      ...(currentData?.brokerParents?.flatMap(
        (parent: any) => parent.brokerFirms
      ) || []),
    ];

    // Extract selectedBrokerFirms by checking both brokerFirms and parents' brokerFirms
    const selectedBrokerFirms = allBrokerFirms.filter((broker: any) =>
      selectedOrgs.some((org: any) => org.brokerFirmId === broker.brokerFirmId)
    );

    // Set selected brokers based on matching broker firms
    setSelectedBrokers(selectedBrokerFirms);
  }, [currentData]);

  const handleEditButtonClick = () => {
    setIsDisabled(false);
  };

  const handleCancelButtonClick = () => {
    setIsDisabled(true);
    router.refresh();
  };

  const canShowButtons = () => {
    if (!canViewWithPerm('addUsers')) return;
    if (isDisabled) {
      return (
        <Button
          onClick={handleEditButtonClick}
          backgroundColor={'blue.600'}
          color={'white'}
        >
          Edit
        </Button>
      );
    } else {
      return (
        <Flex>
          <Button
            onClick={handleCancelButtonClick}
            backgroundColor={'blue.600'}
            color={'white'}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(submitUser)}
            marginLeft={'10px'}
            backgroundColor={'blue.600'}
            color={'white'}
          >
            Save
          </Button>
        </Flex>
      );
    }
  };

  if (loading)
    return (
      <Flex justifyContent={'center'} alignItems={'center'}>
        <Spinner
          thickness="4px"
          speed="0.65s"
          emptyColor="gray.200"
          color="blue.500"
          size="xl"
        />
      </Flex>
    );

  return (
    <Flex borderTopRadius={'12px'} flexDirection={'column'} gap={'20px'}>
      <Flex gap={'20px'} alignItems={'center'}>
        <Box
          background={theme.colors.brand.black}
          borderRadius={'5px'}
          onClick={() => router.back()}
          cursor={'pointer'}
        >
          <BsFillArrowLeftSquareFill
            size={30}
            color={theme.colors.brand.white}
          />
        </Box>
        <Flex>
          <Heading>{'Users'}</Heading>
        </Flex>
      </Flex>
      <Flex justifyContent={'space-between'} alignItems={'center'}>
        <Text fontWeight={700} fontSize={'lg'}>
          {isDisabled ? 'User Detail' : 'Edit User'}
        </Text>
        {canShowButtons()}
      </Flex>
      <Block
        title={'User Information'}
        subTitle={`Last Updated: ${getEditUser?.apManageUserData?.apManageUserProfile?.lastUpdated}`}
        headerRightEnd={
          <Flex gap={2} alignItems="center">
            User Status
            {
              <UserStatusTag
                status={
                  getEditUser?.apManageUserData?.apManageUserProfile
                    ?.auth0Status
                }
              />
            }
          </Flex>
        }
      >
        <UserInfo
          register={register}
          setUserInfo={setUserInfo}
          setUserOrgs={setUserOrgs}
          roles={getUserRoles}
          setUserPermissions={setUserPermissions}
          role={(userInfo.role as string) || ''}
          errors={errors}
          user={userInfo}
          disabled={isDisabled}
          isEdit={true}
        />
      </Block>
      <Access
        setUserOrgs={setUserOrgs}
        role={role}
        userInfo={userInfo}
        orgs={currentData?.apManageUserData?.apManageUserOrgs}
        brokerData={{
          parents: currentData?.brokerParents || [],
          firms: currentData?.brokerFirms || [],
          selectedOrgs, // Adding selectedOrgs
          selectedBrokers, // Adding selectedBrokers
        }}
        defaultInfo={useDefault ? currentData?.apManageUserData : undefined}
        disabled={isDisabled}
        unselectOrgs={unselectOrgs}
        psaAccess={userInfo.psaAccessLevel}
        setUserAccessLevel={setUserAccessLevel}
      />
      {isPermToManageSecureAccess && (
        <SecuredAccess
          secureAccessOrgs={secureAccessOrgs}
          setUserSecuredOrgs={setUserSecuredOrgs}
          role={role}
          userInfo={userInfo}
          disabled={isDisabled}
        />
      )}
      <Permissions
        setUserPermissions={setUserPermissions}
        userPermissions={userPermissions}
        perms={tempPerms || []}
        userInfo={userInfo}
        disabled={isDisabled}
      />
    </Flex>
  );
}
